<Window x:Class="DriverManagementSystem.Views.VehicleManagementWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="إدارة السيارات والسائقين" 
        Height="800" Width="1400"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="#F5F5F5"
        ResizeMode="CanResize">

    <Grid Margin="0,0,0,-29">
        <Grid.RowDefinitions>
            <RowDefinition Height="80"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="60"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" CornerRadius="0,0,10,10">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Column="0" Text="إدارة السيارات والسائقين" 
                          FontSize="24" FontWeight="Bold" Foreground="White"
                          HorizontalAlignment="Center" VerticalAlignment="Center"/>

                <Button Grid.Column="1" 
                        Content="الرئيسية"
                        Background="#FF9800"
                        Foreground="White"
                        BorderBrush="Transparent"
                        Padding="20,10"
                        Margin="20"
                        FontWeight="Bold"
                        Click="CloseWindow_Click"/>
            </Grid>
        </Border>

        <!-- Main Content -->
        <ScrollViewer Grid.Row="1" VerticalScrollBarVisibility="Auto" Margin="10">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- Driver Information Section -->
                <Border Grid.Row="0" Background="White" CornerRadius="8" Margin="5" Padding="15"
                        BorderBrush="#E0E0E0" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>

                    <StackPanel>
                        <TextBlock Text="بيانات السائق" FontSize="18" FontWeight="Bold"
                                  Foreground="#2196F3" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Row 1 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                <TextBlock Text="اسم مالك السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding DriverName, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                <TextBlock Text="رقم التلفون" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding PhoneNumber, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                <TextBlock Text="رقم البطاقة الشخصية" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding CardNumber, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 2 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                <TextBlock Text="نوع البطاقة الشخصية" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding CardTypes}"
                                         SelectedItem="{Binding SelectedCardType}"
                                         Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                <TextBlock Text="مكان الإصدار للبطاقة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding CardIssuePlace, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                <TextBlock Text="تاريخ اصدار البطاقة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <DatePicker SelectedDate="{Binding CardIssueDate}"
                                           Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 3 -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                <TextBlock Text="كود السائق" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding DriverCode, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12" IsReadOnly="True" Background="#F5F5F5"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="1" Margin="5">
                                <TextBlock Text="حالة السائق" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ToggleButton IsChecked="{Binding IsActive, UpdateSourceTrigger=PropertyChanged}"
                                             Height="35" FontSize="12" FontWeight="Bold">
                                    <ToggleButton.Style>
                                        <Style TargetType="ToggleButton">
                                            <Setter Property="Content" Value="غير نشط"/>
                                            <Setter Property="Background" Value="#F44336"/>
                                            <Setter Property="Foreground" Value="White"/>
                                            <Setter Property="BorderBrush" Value="Transparent"/>
                                            <Style.Triggers>
                                                <Trigger Property="IsChecked" Value="True">
                                                    <Setter Property="Content" Value="نشط"/>
                                                    <Setter Property="Background" Value="#4CAF50"/>
                                                </Trigger>
                                            </Style.Triggers>
                                        </Style>
                                    </ToggleButton.Style>
                                </ToggleButton>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Vehicle Information Section -->
                <Border Grid.Row="1" Background="White" CornerRadius="8" Margin="5" Padding="15"
                        BorderBrush="#E0E0E0" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>

                    <StackPanel>
                        <TextBlock Text="بيانات السيارة" FontSize="18" FontWeight="Bold"
                                  Foreground="#2196F3" Margin="0,0,0,15"/>

                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- Row 1 -->
                            <StackPanel Grid.Row="0" Grid.Column="0" Margin="5">
                                <TextBlock Text="نوع السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding VehicleTypes}"
                                         SelectedItem="{Binding SelectedVehicleType}"
                                         Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="1" Margin="5">
                                <TextBlock Text="رقم السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding VehicleNumber, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="0" Grid.Column="2" Margin="5">
                                <TextBlock Text="موديل السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding VehicleModel, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 2 -->
                            <StackPanel Grid.Row="1" Grid.Column="0" Margin="5">
                                <TextBlock Text="قدرة السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding VehicleCapacities}"
                                         SelectedItem="{Binding SelectedVehicleCapacity}"
                                         Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="1" Margin="5">
                                <TextBlock Text="رقم الرخصة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding LicenseNumber, UpdateSourceTrigger=PropertyChanged}"
                                        Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="1" Grid.Column="2" Margin="5">
                                <TextBlock Text="تاريخ الاصدار للرخصة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <DatePicker SelectedDate="{Binding LicenseIssueDate}"
                                           Height="35" FontSize="12"/>
                            </StackPanel>

                            <!-- Row 3 - Additional Fields -->
                            <StackPanel Grid.Row="2" Grid.Column="0" Margin="5">
                                <TextBlock Text="لون السيارة" FontWeight="Bold" Margin="0,0,0,5"/>
                                <ComboBox ItemsSource="{Binding VehicleColors}"
                                         SelectedItem="{Binding SelectedVehicleColor}"
                                         Height="35" FontSize="12"/>
                            </StackPanel>

                            <StackPanel Grid.Row="2" Grid.Column="1" Grid.ColumnSpan="2" Margin="5">
                                <TextBlock Text="ملاحظات" FontWeight="Bold" Margin="0,0,0,5"/>
                                <TextBox Text="{Binding Notes, UpdateSourceTrigger=PropertyChanged}"
                                        Height="60" FontSize="12" TextWrapping="Wrap"
                                        AcceptsReturn="True" VerticalScrollBarVisibility="Auto"/>
                            </StackPanel>
                        </Grid>
                    </StackPanel>
                </Border>

                <!-- Data Grid with Filter -->
                <Border Grid.Row="2" Background="White" CornerRadius="8" Margin="5" Padding="10"
                        BorderBrush="#E0E0E0" BorderThickness="1">
                    <Border.Effect>
                        <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.3"/>
                    </Border.Effect>

                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <!-- Header with Statistics -->
                        <Grid Grid.Row="0" Margin="0,0,0,15">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="Auto"/>
                            </Grid.ColumnDefinitions>

                            <TextBlock Grid.Column="0" Text="قائمة السيارات والسائقين" FontSize="18" FontWeight="Bold"
                                      Foreground="#2196F3" VerticalAlignment="Center"/>

                            <StackPanel Grid.Column="1" Orientation="Horizontal">
                                <TextBlock Text="إجمالي السائقين: " FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding TotalDriversCount}" FontWeight="Bold" Foreground="#4CAF50" VerticalAlignment="Center" Margin="0,0,15,0"/>
                                <TextBlock Text="المعروض: " FontWeight="Bold" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                <TextBlock Text="{Binding FilteredDriversCount}" FontWeight="Bold" Foreground="#2196F3" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Grid>

                        <!-- Filter Section -->
                        <Border Grid.Row="1" Background="#F8F9FA" CornerRadius="5" Padding="10" Margin="0,0,0,10">
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="Auto"/>
                                </Grid.ColumnDefinitions>

                                <StackPanel Grid.Column="0" Margin="5">
                                    <TextBlock Text="البحث بالاسم" FontWeight="Bold" Margin="0,0,0,5" FontSize="11"/>
                                    <TextBox Text="{Binding SearchText, UpdateSourceTrigger=PropertyChanged}"
                                            Height="30" FontSize="11"/>
                                </StackPanel>

                                <StackPanel Grid.Column="1" Margin="5">
                                    <TextBlock Text="نوع السيارة" FontWeight="Bold" Margin="0,0,0,5" FontSize="11"/>
                                    <ComboBox ItemsSource="{Binding VehicleTypesFilter}"
                                             SelectedItem="{Binding SelectedVehicleTypeFilter}"
                                             Height="30" FontSize="11"/>
                                </StackPanel>

                                <StackPanel Grid.Column="2" Margin="5">
                                    <TextBlock Text="قدرة السيارة" FontWeight="Bold" Margin="0,0,0,5" FontSize="11"/>
                                    <ComboBox ItemsSource="{Binding VehicleCapacitiesFilter}"
                                             SelectedItem="{Binding SelectedVehicleCapacityFilter}"
                                             Height="30" FontSize="11"/>
                                </StackPanel>

                                <Button Grid.Column="3" Content="🔍 فلترة"
                                       Height="30" MinWidth="80" Margin="5,20,5,5" FontSize="11" FontWeight="Bold"
                                       Background="#2196F3" Foreground="White" BorderBrush="Transparent"
                                       Command="{Binding ApplyFilterCommand}"/>

                                <Button Grid.Column="4" Content="🔄 إعادة تعيين"
                                       Height="30" MinWidth="100" Margin="5,20,5,5" FontSize="11" FontWeight="Bold"
                                       Background="#FF9800" Foreground="White" BorderBrush="Transparent"
                                       Command="{Binding ClearFilterCommand}"/>
                            </Grid>
                        </Border>

                        <DataGrid Grid.Row="2"
                                 ItemsSource="{Binding FilteredDrivers}"
                                 SelectedItem="{Binding SelectedDriver}"
                                 AutoGenerateColumns="False"
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 GridLinesVisibility="All"
                                 HeadersVisibility="Column"
                                 Background="White"
                                 AlternatingRowBackground="#F5F5F5"
                                 FontSize="11">

                            <DataGrid.Columns>
                                <DataGridTextColumn Header="اسم السائق" Binding="{Binding Name}" Width="140" />
                                <DataGridTextColumn Header="رقم التلفون" Binding="{Binding PhoneNumber}" Width="100" />
                                <DataGridTextColumn Header="رقم البطاقة" Binding="{Binding CardNumber}" Width="110" />
                                <DataGridTextColumn Header="نوع البطاقة" Binding="{Binding CardType}" Width="90" />
                                <DataGridTextColumn Header="مكان الإصدار للبطاقة" Binding="{Binding CardIssuePlace}" Width="120" />
                                <DataGridTextColumn Header="تاريخ اصدار البطاقة" Binding="{Binding CardIssueDate, StringFormat=dd/MM/yyyy}" Width="120" />
                                <DataGridTextColumn Header="رقم الرخصة" Binding="{Binding LicenseNumber}" Width="100" />
                                <DataGridTextColumn Header="تاريخ الإصدار للرخصة" Binding="{Binding LicenseIssueDate, StringFormat=dd/MM/yyyy}" Width="120" />
                                <DataGridTextColumn Header="رقم السيارة" Binding="{Binding VehicleNumber}" Width="100" />
                                <DataGridTextColumn Header="نوع السيارة" Binding="{Binding VehicleType}" Width="110" />
                                <DataGridTextColumn Header="لون السيارة" Binding="{Binding VehicleColor}" Width="90" />
                                <DataGridTextColumn Header="موديل السيارة" Binding="{Binding VehicleModel}" Width="90" />
                                <DataGridTextColumn Header="قدرة السيارة" Binding="{Binding VehicleCapacity}" Width="100" />
                                <DataGridTextColumn Header="كود السائق" Binding="{Binding DriverCode}" Width="90" />

                                <!-- Status Column with Toggle Button -->
                                <DataGridTemplateColumn Header="الحالة" Width="100">
                                    <DataGridTemplateColumn.CellTemplate>
                                        <DataTemplate>
                                            <ToggleButton IsChecked="{Binding IsActive, UpdateSourceTrigger=PropertyChanged}"
                                                         Height="25" Width="70" FontSize="10" FontWeight="Bold">
                                                <ToggleButton.Style>
                                                    <Style TargetType="ToggleButton">
                                                        <Setter Property="Content" Value="غير نشط"/>
                                                        <Setter Property="Background" Value="#F44336"/>
                                                        <Setter Property="Foreground" Value="White"/>
                                                        <Setter Property="BorderBrush" Value="Transparent"/>
                                                        <Style.Triggers>
                                                            <Trigger Property="IsChecked" Value="True">
                                                                <Setter Property="Content" Value="نشط"/>
                                                                <Setter Property="Background" Value="#4CAF50"/>
                                                            </Trigger>
                                                        </Style.Triggers>
                                                    </Style>
                                                </ToggleButton.Style>
                                            </ToggleButton>
                                        </DataTemplate>
                                    </DataGridTemplateColumn.CellTemplate>
                                </DataGridTemplateColumn>
                            </DataGrid.Columns>

                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Height" Value="35"/>
                                </Style>
                            </DataGrid.RowStyle>
                        </DataGrid>
                    </Grid>
                </Border>
            </Grid>
        </ScrollViewer>

        <!-- Action Buttons -->
        <Border Grid.Row="1" Background="#37474F" Padding="10" Margin="0,614,0,30" Grid.RowSpan="2">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Content="إغلاق" 
                       Height="40" MinWidth="100" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#F44336" Foreground="White" BorderBrush="Transparent"
                       Click="CloseWindow_Click"/>

                <Button Content="حذف" 
                       Height="40" MinWidth="100" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#FF9800" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding DeleteCommand}"/>

                <Button Content="إضافة سائق جديد"
                       Height="40" MinWidth="120" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#4CAF50" Foreground="White" BorderBrush="Transparent"
                       Click="AddNewDriverButton_Click"/>

                <Button Content="إضافة سريع"
                       Height="40" MinWidth="100" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#2196F3" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding AddCommand}"/>

                <Button Content="📥 إدراج البيانات الأولية"
                       Height="40" MinWidth="150" Margin="5" FontSize="12" FontWeight="Bold"
                       Background="#9C27B0" Foreground="White" BorderBrush="Transparent"
                       Click="ImportInitialDataButton_Click"
                       ToolTip="إدراج جميع بيانات السائقين الأولية"/>

                <Button Content="حفظ" 
                       Height="40" MinWidth="100" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#2196F3" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding SaveCommand}"/>

                <!-- Navigation Buttons -->
                <Button Content="◀" 
                       Height="40" Width="40" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#607D8B" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding PreviousCommand}"/>

                <Button Content="▶" 
                       Height="40" Width="40" Margin="5" FontSize="14" FontWeight="Bold"
                       Background="#607D8B" Foreground="White" BorderBrush="Transparent"
                       Command="{Binding NextCommand}"/>
            </StackPanel>
        </Border>
    </Grid>
</Window>
