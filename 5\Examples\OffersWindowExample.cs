using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using DriverManagementSystem.Models;
using DriverManagementSystem.Views;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Examples
{
    /// <summary>
    /// مثال على كيفية استخدام نافذة العروض
    /// </summary>
    public static class OffersWindowExample
    {
        /// <summary>
        /// مثال 1: فتح نافذة العروض مع سائقين محددين
        /// </summary>
        /// <param name="selectedDrivers">السائقين المحددين</param>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="daysCount">عدد الأيام</param>
        public static void OpenOffersWindowWithSelectedDrivers(
            ObservableCollection<Driver> selectedDrivers, 
            string visitNumber = "001", 
            int daysCount = 3)
        {
            try
            {
                // التحقق من وجود سائقين محددين
                if (selectedDrivers?.Any() != true)
                {
                    MessageBox.Show("يرجى تحديد سائق واحد على الأقل", "تحذير", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // إنشاء نافذة العروض
                var offersWindow = new OffersWindow(selectedDrivers, visitNumber, daysCount);
                
                // عرض النافذة كـ Dialog
                var result = offersWindow.ShowDialog();
                
                if (result == true)
                {
                    // الحصول على العروض المختارة
                    var selectedOffers = offersWindow.GetSelectedOffers();
                    var winner = offersWindow.GetWinnerOffer();
                    
                    // معالجة النتائج
                    ProcessOffersResults(selectedOffers, winner, visitNumber);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة العروض: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مثال 2: فتح نافذة العروض مع جميع السائقين النشطين
        /// </summary>
        /// <param name="dataService">خدمة البيانات</param>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="daysCount">عدد الأيام</param>
        public static async void OpenOffersWindowWithAllDrivers(
            IDataService dataService, 
            string visitNumber = "001", 
            int daysCount = 3)
        {
            try
            {
                // الحصول على جميع السائقين النشطين
                var allDrivers = await dataService.GetDriversAsync();
                var activeDrivers = new ObservableCollection<Driver>(
                    allDrivers.Where(d => d.IsActive).ToList()
                );

                if (!activeDrivers.Any())
                {
                    MessageBox.Show("لا توجد سائقين نشطين في النظام", "تحذير", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // فتح نافذة العروض
                OpenOffersWindowWithSelectedDrivers(activeDrivers, visitNumber, daysCount);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل السائقين: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مثال 3: فتح نافذة العروض من DataGrid
        /// </summary>
        /// <param name="driversDataGrid">DataGrid السائقين</param>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="daysCount">عدد الأيام</param>
        public static void OpenOffersWindowFromDataGrid(
            System.Windows.Controls.DataGrid driversDataGrid, 
            string visitNumber = "001", 
            int daysCount = 3)
        {
            try
            {
                // الحصول على السائقين المحددين من DataGrid
                var selectedDrivers = new ObservableCollection<Driver>();
                
                foreach (Driver driver in driversDataGrid.SelectedItems)
                {
                    if (driver.IsActive)
                    {
                        selectedDrivers.Add(driver);
                    }
                }

                if (!selectedDrivers.Any())
                {
                    MessageBox.Show("يرجى تحديد سائق نشط واحد على الأقل", "تحذير", 
                                  MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // فتح نافذة العروض
                OpenOffersWindowWithSelectedDrivers(selectedDrivers, visitNumber, daysCount);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الحصول على السائقين المحددين: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مثال 4: فتح نافذة العروض مع خدمة مخصصة
        /// </summary>
        /// <param name="customDataService">خدمة البيانات المخصصة</param>
        /// <param name="visitNumber">رقم الزيارة</param>
        /// <param name="daysCount">عدد الأيام</param>
        public static void OpenOffersWindowWithCustomService(
            IDataService customDataService, 
            string visitNumber = "001", 
            int daysCount = 3)
        {
            try
            {
                // إنشاء نافذة العروض مع خدمة مخصصة
                var offersWindow = new OffersWindow(customDataService);
                
                // تحديث معلومات الزيارة
                offersWindow.UpdateVisitInfo(visitNumber, daysCount);
                
                // عرض النافذة
                var result = offersWindow.ShowDialog();
                
                if (result == true)
                {
                    var selectedOffers = offersWindow.GetSelectedOffers();
                    var winner = offersWindow.GetWinnerOffer();
                    
                    ProcessOffersResults(selectedOffers, winner, visitNumber);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة العروض: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// معالجة نتائج العروض
        /// </summary>
        /// <param name="selectedOffers">العروض المختارة</param>
        /// <param name="winner">السائق الفائز</param>
        /// <param name="visitNumber">رقم الزيارة</param>
        private static void ProcessOffersResults(
            ObservableCollection<DriverOffer> selectedOffers, 
            DriverOffer winner, 
            string visitNumber)
        {
            try
            {
                if (selectedOffers?.Any() == true)
                {
                    var offersText = string.Join(" | ", selectedOffers.Select(o => o.ToSaveString()));
                    
                    var message = $"✅ تم حفظ العروض للزيارة {visitNumber}\n\n";
                    message += $"العروض المختارة:\n{offersText}\n\n";
                    
                    if (winner != null)
                    {
                        message += $"🏆 السائق الفائز: {winner.DriverName} - {winner.FormattedAmount}";
                    }
                    
                    MessageBox.Show(message, "نجح الحفظ", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                    
                    System.Diagnostics.Debug.WriteLine($"✅ تم معالجة {selectedOffers.Count} عرض للزيارة {visitNumber}");
                }
                else
                {
                    MessageBox.Show("لم يتم اختيار أي عروض", "تنبيه", 
                                  MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معالجة النتائج: {ex.Message}", "خطأ", 
                              MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// مثال على كيفية الاستدعاء من زر في النافذة الرئيسية
        /// </summary>
        public static void ButtonClickExample()
        {
            // مثال على كود يوضع في معالج النقر على زر "عروض الأسعار"
            
            /*
            private void OffersButton_Click(object sender, RoutedEventArgs e)
            {
                try
                {
                    // الحصول على السائقين المحددين
                    var selectedDrivers = GetSelectedDriversFromUI();
                    
                    // الحصول على معلومات الزيارة
                    var visitNumber = VisitNumberTextBox.Text;
                    var daysCount = int.Parse(DaysCountTextBox.Text);
                    
                    // فتح نافذة العروض
                    OffersWindowExample.OpenOffersWindowWithSelectedDrivers(
                        selectedDrivers, visitNumber, daysCount);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ: {ex.Message}", "خطأ", 
                                  MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            
            private ObservableCollection<Driver> GetSelectedDriversFromUI()
            {
                var selectedDrivers = new ObservableCollection<Driver>();
                
                // الحصول على السائقين المحددين من DataGrid أو أي عنصر UI آخر
                foreach (Driver driver in DriversDataGrid.SelectedItems)
                {
                    selectedDrivers.Add(driver);
                }
                
                return selectedDrivers;
            }
            */
        }

        /// <summary>
        /// مثال على كيفية التكامل مع ViewModel
        /// </summary>
        public static void ViewModelIntegrationExample()
        {
            // مثال على كود يوضع في ViewModel
            
            /*
            public class MainViewModel : BindableBase
            {
                private IDataService _dataService;
                
                public DelegateCommand OpenOffersCommand { get; }
                
                public MainViewModel(IDataService dataService)
                {
                    _dataService = dataService;
                    OpenOffersCommand = new DelegateCommand(ExecuteOpenOffers, CanExecuteOpenOffers);
                }
                
                private void ExecuteOpenOffers()
                {
                    var selectedDrivers = GetSelectedDrivers();
                    var visitNumber = CurrentVisitNumber;
                    var daysCount = CurrentDaysCount;
                    
                    OffersWindowExample.OpenOffersWindowWithSelectedDrivers(
                        selectedDrivers, visitNumber, daysCount);
                }
                
                private bool CanExecuteOpenOffers()
                {
                    return GetSelectedDrivers()?.Any() == true;
                }
                
                private ObservableCollection<Driver> GetSelectedDrivers()
                {
                    return new ObservableCollection<Driver>(
                        AllDrivers.Where(d => d.IsSelected && d.IsActive).ToList()
                    );
                }
            }
            */
        }
    }
}
