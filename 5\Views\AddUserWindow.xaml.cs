using System;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class AddUserWindow : Window
    {
        private readonly IUserService _userService;
        public event EventHandler? UserAdded;

        public AddUserWindow(IUserService userService)
        {
            InitializeComponent();
            _userService = userService;
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            // Validate input
            if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال الاسم الكامل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                FullNameTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال اسم المستخدم", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                UsernameTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                MessageBox.Show("يرجى إدخال البريد الإلكتروني", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                EmailTextBox.Focus();
                return;
            }

            // Validate email format
            if (!IsValidEmail(EmailTextBox.Text.Trim()))
            {
                MessageBox.Show("يرجى إدخال بريد إلكتروني صحيح!", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                EmailTextBox.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                MessageBox.Show("يرجى إدخال كلمة المرور", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                PasswordBox.Focus();
                return;
            }

            if (PasswordBox.Password.Length < 6)
            {
                MessageBox.Show("كلمة المرور يجب أن تكون 6 أحرف على الأقل", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                PasswordBox.Focus();
                return;
            }

            try
            {
                // Show loading state
                SaveButton.IsEnabled = false;
                SaveButton.Content = "⏳ جاري الحفظ...";
                CancelButton.IsEnabled = false;

                var selectedRole = ((ComboBoxItem)RoleComboBox.SelectedItem).Tag.ToString();

                var newUser = new User
                {
                    FullName = FullNameTextBox.Text.Trim(),
                    Username = UsernameTextBox.Text.Trim(),
                    Email = EmailTextBox.Text.Trim(),
                    Role = selectedRole ?? "User",
                    IsActive = IsActiveCheckBox.IsChecked ?? true,
                    Notes = NotesTextBox.Text.Trim(),
                    CreatedBy = "admin" // Should be current user
                };

                var success = await _userService.CreateUserAsync(newUser, PasswordBox.Password);

                if (success)
                {
                    MessageBox.Show("تم إضافة المستخدم بنجاح!", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    UserAdded?.Invoke(this, EventArgs.Empty);
                    this.Close();
                }
                else
                {
                    MessageBox.Show("فشل في إضافة المستخدم. قد يكون اسم المستخدم موجود مسبقاً.", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            finally
            {
                // Restore button state
                SaveButton.IsEnabled = true;
                SaveButton.Content = "💾 حفظ";
                CancelButton.IsEnabled = true;
            }
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
