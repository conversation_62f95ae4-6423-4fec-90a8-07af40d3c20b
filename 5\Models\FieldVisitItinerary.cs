using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.ComponentModel;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// نموذج خط السير للزيارة الميدانية - تصميم احترافي
    /// يدعم تخزين تفاصيل خط السير لكل يوم بشكل منفصل ومرن
    /// </summary>
    [Table("FieldVisitItineraries")]
    [DisplayName("خط السير اليومي")]
    public class FieldVisitItinerary
    {
        /// <summary>
        /// المعرف الفريد لسجل خط السير
        /// </summary>
        [Key]
        [DatabaseGenerated(DatabaseGeneratedOption.Identity)]
        public int Id { get; set; }

        /// <summary>
        /// معرف الزيارة الميدانية المرتبطة
        /// </summary>
        [Required(ErrorMessage = "معرف الزيارة الميدانية مطلوب")]
        [ForeignKey("FieldVisit")]
        public int FieldVisitId { get; set; }

        /// <summary>
        /// رقم اليوم في خط السير (1، 2، 3...)
        /// </summary>
        [Required(ErrorMessage = "رقم اليوم مطلوب")]
        [Range(1, 365, ErrorMessage = "رقم اليوم يجب أن يكون بين 1 و 365")]
        [DisplayName("رقم اليوم")]
        public int DayNumber { get; set; }

        /// <summary>
        /// تفاصيل خط السير لهذا اليوم
        /// </summary>
        public string ItineraryText { get; set; } = string.Empty;

        /// <summary>
        /// تاريخ إنشاء السجل
        /// </summary>
        [Required]
        [DisplayName("تاريخ الإنشاء")]
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        /// <summary>
        /// تاريخ آخر تحديث
        /// </summary>
        [DisplayName("تاريخ التحديث")]
        public DateTime? UpdatedAt { get; set; }

        /// <summary>
        /// ملاحظات إضافية (اختيارية)
        /// </summary>
        [StringLength(500, ErrorMessage = "الملاحظات لا يجب أن تتجاوز 500 حرف")]
        [DisplayName("ملاحظات")]
        public string? Notes { get; set; }

        /// <summary>
        /// حالة السجل (نشط/غير نشط)
        /// </summary>
        [Required]
        [DisplayName("حالة السجل")]
        public bool IsActive { get; set; } = true;

        // Navigation Properties
        /// <summary>
        /// الزيارة الميدانية المرتبطة
        /// </summary>
        [ForeignKey("FieldVisitId")]
        public virtual FieldVisit? FieldVisit { get; set; }

        /// <summary>
        /// خاصية محسوبة لعرض معلومات مختصرة
        /// </summary>
        [NotMapped]
        [DisplayName("ملخص اليوم")]
        public string DaySummary => $"اليوم {DayNumber}: {(ItineraryText.Length > 50 ? ItineraryText.Substring(0, 50) + "..." : ItineraryText)}";

        /// <summary>
        /// خاصية محسوبة للتحقق من صحة البيانات
        /// </summary>
        [NotMapped]
        public bool IsValid => !string.IsNullOrWhiteSpace(ItineraryText) && DayNumber > 0 && FieldVisitId > 0;

        /// <summary>
        /// تحديث تاريخ التعديل عند تغيير البيانات
        /// </summary>
        public void MarkAsUpdated()
        {
            UpdatedAt = DateTime.Now;
        }

        /// <summary>
        /// إرجاع تمثيل نصي للكائن
        /// </summary>
        /// <returns></returns>
        public override string ToString()
        {
            return $"اليوم {DayNumber}: {ItineraryText}";
        }
    }
}
