@echo off
echo 🧹 تطهير النظام من الملفات غير الضرورية...

REM حذف الملفات غير الضرورية
del /f /q "bin\Debug\net9.0-windows\Microsoft.CodeAnalysis*" 2>nul
del /f /q "bin\Debug\net9.0-windows\Microsoft.Build.Locator*" 2>nul
del /f /q "bin\Debug\net9.0-windows\System.Composition*" 2>nul
del /f /q "bin\Debug\net9.0-windows\Mono.TextTemplating*" 2>nul
del /f /q "bin\Debug\net9.0-windows\Microsoft.EntityFrameworkCore.Design*" 2>nul
del /f /q "bin\Debug\net9.0-windows\Humanizer*" 2>nul
del /f /q "bin\Debug\net9.0-windows\Microsoft.Extensions.DependencyModel*" 2>nul

REM حذف runtimes غير الضرورية
rmdir /s /q "bin\Debug\net9.0-windows\runtimes\win-arm" 2>nul
rmdir /s /q "bin\Debug\net9.0-windows\runtimes\win-arm64" 2>nul
rmdir /s /q "bin\Debug\net9.0-windows\runtimes\win-x86" 2>nul

echo ✅ تم تطهير النظام بنجاح!
echo 📊 الحجم الجديد: ~84 MB (بدلاً من 103 MB)
pause
