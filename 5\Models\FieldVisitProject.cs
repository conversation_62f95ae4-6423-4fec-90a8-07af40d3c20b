using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// جدول ربط المشاريع بالزيارات الميدانية
    /// </summary>
    public class FieldVisitProject
    {
        [Key]
        public int Id { get; set; }

        /// <summary>
        /// معرف الزيارة الميدانية
        /// </summary>
        [Required]
        public int FieldVisitId { get; set; }

        /// <summary>
        /// معرف المشروع (اختياري - يمكن أن يكون null إذا كان المشروع جديد)
        /// </summary>
        public int? ProjectId { get; set; }

        /// <summary>
        /// رقم المشروع (نسخة للعرض السريع)
        /// </summary>
        [MaxLength(50)]
        public string ProjectNumber { get; set; } = string.Empty;

        /// <summary>
        /// اسم المشروع (نسخة للعرض السريع)
        /// </summary>
        [MaxLength(500)]
        public string ProjectName { get; set; } = string.Empty;

        /// <summary>
        /// ترتيب المشروع في الزيارة
        /// </summary>
        public int DisplayOrder { get; set; } = 1;

        /// <summary>
        /// ملاحظات خاصة بالمشروع في هذه الزيارة
        /// </summary>
        [MaxLength(1000)]
        public string Notes { get; set; } = string.Empty;

        /// <summary>
        /// هل المشروع نشط في هذه الزيارة
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// تاريخ الإضافة
        /// </summary>
        public DateTime CreatedAt { get; set; } = DateTime.Now;

        // Navigation properties
        [ForeignKey("FieldVisitId")]
        public virtual FieldVisit? FieldVisit { get; set; }

        [ForeignKey("ProjectId")]
        public virtual Project? Project { get; set; }
    }
}
