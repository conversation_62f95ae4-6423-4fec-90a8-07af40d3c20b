<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!-- Enhanced Print Page Style -->
    <Style x:Key="PrintPageStyle" TargetType="Border">
        <Setter Property="Width" Value="750"/>
        <Setter Property="MinHeight" Value="1123"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#333333"/>
        <Setter Property="BorderThickness" Value="2"/>
        <Setter Property="Margin" Value="0"/>
        <Setter Property="Padding" Value="20"/>
    </Style>

    <!-- Enhanced Page Break Style -->
    <Style x:Key="PageBreakStyle" TargetType="Border">
        <Setter Property="Height" Value="30"/>
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,0">
                    <GradientStop Color="Transparent" Offset="0"/>
                    <GradientStop Color="#E0E0E0" Offset="0.5"/>
                    <GradientStop Color="Transparent" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="0"/>
        <Setter Property="Margin" Value="0,15,0,15"/>
    </Style>

    <!-- Professional Header Style -->
    <Style x:Key="ProfessionalHeaderStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontWeight" Value="Bold"/>
        <Setter Property="FontSize" Value="16"/>
        <Setter Property="Foreground" Value="#333333"/>
        <Setter Property="TextAlignment" Value="Center"/>
        <Setter Property="Margin" Value="0,10,0,10"/>
    </Style>

    <!-- Professional Content Style -->
    <Style x:Key="ProfessionalContentStyle" TargetType="TextBlock">
        <Setter Property="FontFamily" Value="Segoe UI"/>
        <Setter Property="FontSize" Value="12"/>
        <Setter Property="Foreground" Value="#333333"/>
        <Setter Property="LineHeight" Value="20"/>
        <Setter Property="TextWrapping" Value="Wrap"/>
        <Setter Property="TextAlignment" Value="Justify"/>
    </Style>

    <!-- Enhanced Table Header Style -->
    <Style x:Key="TableHeaderStyle" TargetType="Border">
        <Setter Property="Background" Value="#F8F9FA"/>
        <Setter Property="BorderBrush" Value="#333333"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="10,8"/>
    </Style>

    <!-- Enhanced Table Cell Style -->
    <Style x:Key="TableCellStyle" TargetType="Border">
        <Setter Property="Background" Value="White"/>
        <Setter Property="BorderBrush" Value="#DDDDDD"/>
        <Setter Property="BorderThickness" Value="1"/>
        <Setter Property="Padding" Value="8,6"/>
        <Setter Property="MinHeight" Value="30"/>
    </Style>

    <!-- Print Container Style -->
    <Style x:Key="PrintContainerStyle" TargetType="StackPanel">
        <Setter Property="Orientation" Value="Vertical"/>
        <Setter Property="Background" Value="White"/>
        <Setter Property="Margin" Value="10"/>
    </Style>

</ResourceDictionary>
