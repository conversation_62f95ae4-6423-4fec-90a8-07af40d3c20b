using System.Collections.Generic;
using System.Threading.Tasks;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// واجهة خدمة الزيارات الميدانية
    /// </summary>
    public interface IFieldVisitService
    {
        /// <summary>
        /// بناء رسالة الزيارة من معرفات الزيارات المحددة
        /// </summary>
        /// <param name="fieldVisitIds">معرفات الزيارات الميدانية</param>
        /// <returns>محتوى الرسالة الجاهز</returns>
        Task<string> BuildVisitMessageAsync(IEnumerable<int> fieldVisitIds);

        /// <summary>
        /// بناء رسالة لسائق محدد من زيارة محددة
        /// </summary>
        /// <param name="fieldVisitId">معرف الزيارة</param>
        /// <param name="driverName">اسم السائق</param>
        /// <returns>محتوى الرسالة المخصصة</returns>
        Task<string> BuildDriverMessageAsync(int fieldVisitId, string driverName);

        /// <summary>
        /// الحصول على جميع الزيارات المحددة
        /// </summary>
        /// <param name="fieldVisitIds">معرفات الزيارات</param>
        /// <returns>قائمة الزيارات</returns>
        Task<List<Models.FieldVisit>> GetSelectedVisitsAsync(IEnumerable<int> fieldVisitIds);
    }
}
