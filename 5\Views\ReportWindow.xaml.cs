using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Documents;
using System.Windows.Media;
using System.Windows.Xps;
using System.Windows.Xps.Packaging;
using System.IO;
using Microsoft.Win32;
using System.Printing;
using System.Windows.Media.Imaging;
using iTextSharp.text;
using iTextSharp.text.pdf;
using DriverManagementSystem.ViewModels;
using PdfParagraph = iTextSharp.text.Paragraph;

namespace DriverManagementSystem.Views
{
    public partial class ReportWindow : Window
    {
        public ReportWindow()
        {
            InitializeComponent();
            DataContext = new ReportViewModel();
        }

        /// <summary>
        /// إنشاء نافذة التقرير مع زيارة محددة
        /// </summary>
        /// <param name="selectedVisit">الزيارة المحددة من DataGrid</param>
        public ReportWindow(DriverManagementSystem.Models.FieldVisit selectedVisit) : this()
        {
            if (selectedVisit != null && DataContext is ReportViewModel viewModel)
            {
                System.Diagnostics.Debug.WriteLine($"🔍 ReportWindow Constructor with FieldVisit:");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit.VisitNumber: {selectedVisit.VisitNumber}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit.DepartureDate: {selectedVisit.DepartureDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit.ReturnDate: {selectedVisit.ReturnDate:dd/MM/yyyy}");
                System.Diagnostics.Debug.WriteLine($"🔍 Visit.DaysCount: {selectedVisit.DaysCount}");

                // تحديد الزيارة المحددة في ViewModel
                viewModel.SetSelectedVisit(selectedVisit);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ShowPrintPreview();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في معاينة الطباعة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        private void ExportPdfButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                ExportToPdf();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ShowPrintPreview()
        {
            // Get the report content
            var reportView = FindReportView();
            if (reportView == null)
            {
                MessageBox.Show("لا يوجد تقرير للمعاينة", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Create and show print preview window
            var previewWindow = new PrintPreviewWindow(reportView);
            previewWindow.ShowDialog();
        }

        private void ExportToPdf()
        {
            // Get the report content
            var reportView = FindReportView();
            if (reportView == null)
            {
                MessageBox.Show("لا يوجد تقرير للتصدير", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // Show save dialog
            SaveFileDialog saveDialog = new SaveFileDialog
            {
                Filter = "PDF Files (*.pdf)|*.pdf",
                DefaultExt = "pdf",
                FileName = $"محضر_استخراج_عروض_الأسعار_{DateTime.Now:yyyy-MM-dd_HH-mm}.pdf"
            };

            if (saveDialog.ShowDialog() == true)
            {
                try
                {
                    CreatePdfFromReport(reportView, saveDialog.FileName);

                    MessageBox.Show($"تم تصدير PDF بنجاح إلى:\n{saveDialog.FileName}",
                                  "نجح", MessageBoxButton.OK, MessageBoxImage.Information);

                    // Ask if user wants to open the file
                    var result = MessageBox.Show("هل تريد فتح الملف الآن؟", "فتح الملف",
                        MessageBoxButton.YesNo, MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo
                        {
                            FileName = saveDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"خطأ في تصدير PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private ReportView FindReportView()
        {
            // Find the ReportView in the visual tree
            return FindVisualChild<ReportView>(this);
        }

        private static T FindVisualChild<T>(DependencyObject parent) where T : DependencyObject
        {
            for (int i = 0; i < VisualTreeHelper.GetChildrenCount(parent); i++)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);
                if (child is T)
                    return (T)child;

                T childOfChild = FindVisualChild<T>(child);
                if (childOfChild != null)
                    return childOfChild;
            }
            return null;
        }

        private FrameworkElement CreatePrintableReport(ReportView originalReport)
        {
            // A4 dimensions in pixels at 96 DPI (8.27×11.69 inches)
            double a4Width = 794;   // 8.27 inches × 96 DPI
            double a4Height = 1123; // 11.69 inches × 96 DPI

            // Create a container that fills the entire A4 page
            var printContainer = new Grid
            {
                Background = Brushes.White,
                Width = a4Width,
                Height = a4Height,
                HorizontalAlignment = HorizontalAlignment.Left,
                VerticalAlignment = VerticalAlignment.Top
            };

            // Add content with minimal margins to maximize space usage
            var contentBorder = new Border
            {
                Margin = new Thickness(20), // Minimal margins
                Background = Brushes.Transparent,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch
            };

            // Clone the report content with proper sizing
            var reportClone = new ReportView
            {
                DataContext = originalReport.DataContext,
                HorizontalAlignment = HorizontalAlignment.Stretch,
                VerticalAlignment = VerticalAlignment.Stretch,
                Width = a4Width - 40, // Account for margins
                Height = a4Height - 40
            };

            contentBorder.Child = reportClone;
            printContainer.Children.Add(contentBorder);

            // Force proper layout at exact A4 dimensions
            printContainer.Measure(new Size(a4Width, a4Height));
            printContainer.Arrange(new Rect(0, 0, a4Width, a4Height));
            printContainer.UpdateLayout();

            // Force the report clone to update its layout too
            reportClone.Measure(new Size(a4Width - 40, a4Height - 40));
            reportClone.Arrange(new Rect(0, 0, a4Width - 40, a4Height - 40));
            reportClone.UpdateLayout();

            return printContainer;
        }

        private void CreatePdfFromReport(FrameworkElement reportElement, string fileName)
        {
            try
            {
                // Create PDF with exact A4 dimensions to prevent cropping
                CreateDirectPdfReport(reportElement as ReportView, fileName);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إنشاء PDF: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CreateDirectPdfReport(ReportView reportView, string fileName)
        {
            // Create PDF with exact A4 dimensions (210×297mm)
            var document = new iTextSharp.text.Document(PageSize.A4, 20, 20, 20, 20);

            using (var stream = new FileStream(fileName, FileMode.Create))
            {
                var writer = PdfWriter.GetInstance(document, stream);
                document.Open();

                // Get report data
                var reportData = reportView?.DataContext as ViewModels.ReportViewModel;
                if (reportData?.ReportData != null)
                {
                    CreateTextBasedPdfContent(document, reportData.ReportData);
                }
                else
                {
                    // Fallback: Create sample content
                    CreateSamplePdfContent(document);
                }

                document.Close();
            }
        }

        private void CreateSamplePdfContent(iTextSharp.text.Document document)
        {
            // Arabic font setup
            string fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Fonts), "arial.ttf");
            if (!File.Exists(fontPath))
            {
                fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts", "arial.ttf");
            }

            BaseFont arabicFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            var titleFont = new iTextSharp.text.Font(arabicFont, 18, iTextSharp.text.Font.BOLD);
            var headerFont = new iTextSharp.text.Font(arabicFont, 14, iTextSharp.text.Font.BOLD);
            var normalFont = new iTextSharp.text.Font(arabicFont, 12, iTextSharp.text.Font.NORMAL);

            // Header
            var headerTable = new PdfPTable(2);
            headerTable.WidthPercentage = 100;
            headerTable.SetWidths(new float[] { 1, 1 });

            var dateCell = new PdfPCell(new Phrase("التاريخ: 14/07/2025", normalFont));
            dateCell.Border = Rectangle.NO_BORDER;
            dateCell.HorizontalAlignment = Element.ALIGN_RIGHT;
            headerTable.AddCell(dateCell);

            var visitCell = new PdfPCell(new Phrase("رقم الزيارة: 001", normalFont));
            visitCell.Border = Rectangle.NO_BORDER;
            visitCell.HorizontalAlignment = Element.ALIGN_LEFT;
            headerTable.AddCell(visitCell);

            document.Add(headerTable);
            document.Add(new PdfParagraph(" "));

            // Organization
            var orgPara = new PdfParagraph("الجمهورية اليمنية", headerFont);
            orgPara.Alignment = Element.ALIGN_CENTER;
            document.Add(orgPara);

            var deptPara = new PdfParagraph("المجلس المحلي للمديرية", normalFont);
            deptPara.Alignment = Element.ALIGN_CENTER;
            document.Add(deptPara);

            var branchPara = new PdfParagraph("فرع عدن والمحافظات", normalFont);
            branchPara.Alignment = Element.ALIGN_CENTER;
            document.Add(branchPara);

            document.Add(new PdfParagraph(" "));

            // Title
            var titlePara = new PdfParagraph("محضر استخراج عروض أسعار", titleFont);
            titlePara.Alignment = Element.ALIGN_CENTER;
            titlePara.SpacingAfter = 20;
            document.Add(titlePara);

            // Projects section
            var projectsTitle = new PdfParagraph("المشاريع التي سيتم زيارتها", headerFont);
            projectsTitle.SpacingBefore = 10;
            document.Add(projectsTitle);

            // Sample projects
            document.Add(new PdfParagraph("1 - مشروع تطوير الطرق الريفية", normalFont));
            document.Add(new PdfParagraph("2 - مشروع تحسين شبكة المياه", normalFont));
            document.Add(new PdfParagraph("3 - مشروع الطاقة الشمسية", normalFont));

            document.Add(new PdfParagraph(" "));

            // Technical data
            var techTitle = new PdfParagraph("البيانات الفنية", headerFont);
            document.Add(techTitle);

            document.Add(new PdfParagraph("طبيعة التشغيل: زيارة استطلاعية لمكونات المشروع", normalFont));
            document.Add(new PdfParagraph("القائم بالزيارة: مهندس المشروع", normalFont));
            document.Add(new PdfParagraph("خط السير: عدن - لحج - أبين", normalFont));

            document.Add(new PdfParagraph(" "));

            // Price offers table
            var offersTitle = new PdfParagraph("قائمة الأسعار المقدمة من السائقين", headerFont);
            document.Add(offersTitle);

            var offersTable = new PdfPTable(4);
            offersTable.WidthPercentage = 100;
            offersTable.SetWidths(new float[] { 1, 3, 2, 2 });

            // Headers
            offersTable.AddCell(new PdfPCell(new Phrase("الرقم", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
            offersTable.AddCell(new PdfPCell(new Phrase("اسم السائق", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
            offersTable.AddCell(new PdfPCell(new Phrase("رقم التلفون", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
            offersTable.AddCell(new PdfPCell(new Phrase("المبلغ", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });

            // Sample data
            offersTable.AddCell(new PdfPCell(new Phrase("1", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
            offersTable.AddCell(new PdfPCell(new Phrase("أحمد محمد علي", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
            offersTable.AddCell(new PdfPCell(new Phrase("777123456", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
            offersTable.AddCell(new PdfPCell(new Phrase("50,000", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });

            offersTable.AddCell(new PdfPCell(new Phrase("2", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
            offersTable.AddCell(new PdfPCell(new Phrase("محمد أحمد سالم", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
            offersTable.AddCell(new PdfPCell(new Phrase("777654321", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
            offersTable.AddCell(new PdfPCell(new Phrase("45,000", normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });

            document.Add(offersTable);
            document.Add(new PdfParagraph(" "));

            // Duration
            var durationTable = new PdfPTable(3);
            durationTable.WidthPercentage = 100;

            durationTable.AddCell(new PdfPCell(new Phrase("تاريخ النزول: 15/07/2025", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase("تاريخ العودة: 18/07/2025", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase("عدد الأيام: 3", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            document.Add(durationTable);
            document.Add(new PdfParagraph(" "));

            // Selected driver
            var driverTitle = new PdfParagraph("السائق المختار وبيانات السيارة", headerFont);
            document.Add(driverTitle);

            var driverTable = new PdfPTable(2);
            driverTable.WidthPercentage = 100;

            driverTable.AddCell(new PdfPCell(new Phrase("السائق: محمد أحمد سالم", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase("سنة الصنع: 2020", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase("التلفون: 777654321", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase("اللون: أبيض", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase("نوع السيارة: تويوتا هايلكس", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase("رقم اللوحة: ع د ن 12345", normalFont)) { Border = Rectangle.NO_BORDER });

            document.Add(driverTable);
            document.Add(new PdfParagraph(" "));

            // Signatures
            var signaturesTable = new PdfPTable(3);
            signaturesTable.WidthPercentage = 100;
            signaturesTable.SpacingBefore = 30;

            signaturesTable.AddCell(new PdfPCell(new Phrase("المكلف بالمهمة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("مسئول الحركة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("يعتمد", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            signaturesTable.AddCell(new PdfPCell(new Phrase("م. علي أحمد", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase("أ. محمد سالم", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase("د. أحمد محمد\nمدير الفرع", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });

            document.Add(signaturesTable);
        }

        private void CreateTextBasedPdfContent(iTextSharp.text.Document document, Models.ReportModel data)
        {
            // Arabic font setup
            string fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Fonts), "arial.ttf");
            if (!File.Exists(fontPath))
            {
                fontPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Windows), "Fonts", "arial.ttf");
            }

            BaseFont arabicFont = BaseFont.CreateFont(fontPath, BaseFont.IDENTITY_H, BaseFont.EMBEDDED);
            var titleFont = new iTextSharp.text.Font(arabicFont, 18, iTextSharp.text.Font.BOLD);
            var headerFont = new iTextSharp.text.Font(arabicFont, 14, iTextSharp.text.Font.BOLD);
            var normalFont = new iTextSharp.text.Font(arabicFont, 12, iTextSharp.text.Font.NORMAL);

            // Header section
            var headerTable = new PdfPTable(2);
            headerTable.WidthPercentage = 100;
            headerTable.SetWidths(new float[] { 1, 1 });

            var dateCell = new PdfPCell(new Phrase($"التاريخ: {data.ReportDate}", normalFont));
            dateCell.Border = Rectangle.NO_BORDER;
            dateCell.HorizontalAlignment = Element.ALIGN_RIGHT;
            headerTable.AddCell(dateCell);

            var visitCell = new PdfPCell(new Phrase($"رقم الزيارة: {data.VisitNumber}", normalFont));
            visitCell.Border = Rectangle.NO_BORDER;
            visitCell.HorizontalAlignment = Element.ALIGN_LEFT;
            headerTable.AddCell(visitCell);

            document.Add(headerTable);
            document.Add(new PdfParagraph(" "));

            // Organization info
            var orgPara = new PdfParagraph("الجمهورية اليمنية", headerFont);
            orgPara.Alignment = Element.ALIGN_CENTER;
            document.Add(orgPara);

            var deptPara = new PdfParagraph("المجلس المحلي للمديرية", normalFont);
            deptPara.Alignment = Element.ALIGN_CENTER;
            document.Add(deptPara);

            var branchPara = new PdfParagraph("فرع عدن والمحافظات", normalFont);
            branchPara.Alignment = Element.ALIGN_CENTER;
            document.Add(branchPara);

            document.Add(new PdfParagraph(" "));

            // Title
            var titlePara = new PdfParagraph("محضر استخراج عروض أسعار", titleFont);
            titlePara.Alignment = Element.ALIGN_CENTER;
            titlePara.SpacingAfter = 20;
            document.Add(titlePara);

            // Projects section
            var projectsTitle = new PdfParagraph("المشاريع التي سيتم زيارتها", headerFont);
            projectsTitle.SpacingBefore = 10;
            document.Add(projectsTitle);

            if (data.Projects != null && data.Projects.Count > 0)
            {
                foreach (var project in data.Projects)
                {
                    var projectPara = new PdfParagraph($"{project.SerialNumber} - {project.ProjectNumber} - {project.ProjectName}", normalFont);
                    projectPara.IndentationLeft = 20;
                    document.Add(projectPara);
                }
            }

            document.Add(new PdfParagraph(" "));

            // Technical data section
            var techTitle = new PdfParagraph("البيانات الفنية", headerFont);
            document.Add(techTitle);

            document.Add(new PdfParagraph($"طبيعة التشغيل: {data.VisitNature}", normalFont));
            document.Add(new PdfParagraph($"القائم بالزيارة: {data.VisitConductor}", normalFont));
            document.Add(new PdfParagraph($"خط السير: {data.RouteDescription}", normalFont));

            document.Add(new PdfParagraph(" "));

            // Price offers table
            var offersTitle = new PdfParagraph("قائمة الأسعار المقدمة من السائقين", headerFont);
            document.Add(offersTitle);

            if (data.PriceOffers != null && data.PriceOffers.Count > 0)
            {
                var offersTable = new PdfPTable(4);
                offersTable.WidthPercentage = 100;
                offersTable.SetWidths(new float[] { 1, 3, 2, 2 });

                // Headers
                offersTable.AddCell(new PdfPCell(new Phrase("الرقم", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("اسم السائق", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("رقم التلفون", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                offersTable.AddCell(new PdfPCell(new Phrase("المبلغ", headerFont)) { HorizontalAlignment = Element.ALIGN_CENTER });

                // Data
                foreach (var offer in data.PriceOffers)
                {
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.SerialNumber.ToString(), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.DriverName, normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.PhoneNumber, normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                    offersTable.AddCell(new PdfPCell(new Phrase(offer.OfferedPrice.ToString("N0"), normalFont)) { HorizontalAlignment = Element.ALIGN_CENTER });
                }

                document.Add(offersTable);
            }

            document.Add(new PdfParagraph(" "));

            // Duration section
            var durationTable = new PdfPTable(3);
            durationTable.WidthPercentage = 100;

            durationTable.AddCell(new PdfPCell(new Phrase($"تاريخ النزول: {data.DepartureDate}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase($"تاريخ العودة: {data.ReturnDate}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            durationTable.AddCell(new PdfPCell(new Phrase($"عدد الأيام: {data.DaysCount}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            document.Add(durationTable);
            document.Add(new PdfParagraph(" "));

            // Selected driver section
            var driverTitle = new PdfParagraph("السائق المختار وبيانات السيارة", headerFont);
            document.Add(driverTitle);

            var driverTable = new PdfPTable(2);
            driverTable.WidthPercentage = 100;

            driverTable.AddCell(new PdfPCell(new Phrase($"السائق: {data.SelectedDriverName}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"سنة الصنع: {data.VehicleModel}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"التلفون: {data.SelectedDriverPhone}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"اللون: {data.VehicleColor}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"نوع السيارة: {data.VehicleType}", normalFont)) { Border = Rectangle.NO_BORDER });
            driverTable.AddCell(new PdfPCell(new Phrase($"رقم اللوحة: {data.PlateNumber}", normalFont)) { Border = Rectangle.NO_BORDER });

            document.Add(driverTable);
            document.Add(new PdfParagraph(" "));

            // Signatures section
            var signaturesTable = new PdfPTable(3);
            signaturesTable.WidthPercentage = 100;
            signaturesTable.SpacingBefore = 30;

            signaturesTable.AddCell(new PdfPCell(new Phrase("المكلف بالمهمة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("مسئول الحركة", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });
            signaturesTable.AddCell(new PdfPCell(new Phrase("يعتمد", headerFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER });

            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.TaskManagerName}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.MovementResponsibleName}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });
            signaturesTable.AddCell(new PdfPCell(new Phrase($"{data.BranchManagerName}\n{data.BranchManagerTitle}", normalFont)) { Border = Rectangle.NO_BORDER, HorizontalAlignment = Element.ALIGN_CENTER, PaddingTop = 30 });

            document.Add(signaturesTable);
        }
    }
}
