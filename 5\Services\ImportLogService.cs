using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Threading.Tasks;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة سجل عمليات الاستيراد من Excel
    /// </summary>
    public class ImportLogService
    {
        private readonly string _logFilePath;
        private readonly string _logDirectory;

        public ImportLogService()
        {
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SFDSystem");
            _logDirectory = Path.Combine(appDataPath, "ImportLogs");
            Directory.CreateDirectory(_logDirectory);
            
            _logFilePath = Path.Combine(_logDirectory, "ExcelImportLog.json");
        }

        /// <summary>
        /// تسجيل عملية استيراد ناجحة
        /// </summary>
        public async Task LogSuccessfulImportAsync(ImportLogEntry logEntry)
        {
            try
            {
                var logs = await LoadLogsAsync();
                logEntry.Id = Guid.NewGuid().ToString();
                logEntry.Timestamp = DateTime.Now;
                logEntry.Status = ImportStatus.Success;
                
                logs.Add(logEntry);
                
                // الاحتفاظ بآخر 100 عملية فقط
                if (logs.Count > 100)
                {
                    logs.RemoveRange(0, logs.Count - 100);
                }
                
                await SaveLogsAsync(logs);
                
                System.Diagnostics.Debug.WriteLine($"✅ تم تسجيل عملية استيراد ناجحة: {logEntry.FileName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تسجيل عملية الاستيراد: {ex.Message}");
            }
        }

        /// <summary>
        /// تسجيل عملية استيراد فاشلة
        /// </summary>
        public async Task LogFailedImportAsync(string fileName, string errorMessage, string indexValue = "")
        {
            try
            {
                var logEntry = new ImportLogEntry
                {
                    Id = Guid.NewGuid().ToString(),
                    Timestamp = DateTime.Now,
                    FileName = fileName,
                    IndexValue = indexValue,
                    Status = ImportStatus.Failed,
                    ErrorMessage = errorMessage
                };

                var logs = await LoadLogsAsync();
                logs.Add(logEntry);
                
                if (logs.Count > 100)
                {
                    logs.RemoveRange(0, logs.Count - 100);
                }
                
                await SaveLogsAsync(logs);
                
                System.Diagnostics.Debug.WriteLine($"❌ تم تسجيل عملية استيراد فاشلة: {fileName}");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تسجيل عملية الاستيراد الفاشلة: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على آخر عمليات الاستيراد
        /// </summary>
        public async Task<List<ImportLogEntry>> GetRecentImportsAsync(int count = 20)
        {
            try
            {
                var logs = await LoadLogsAsync();
                return logs.OrderByDescending(l => l.Timestamp)
                          .Take(count)
                          .ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب سجل الاستيراد: {ex.Message}");
                return new List<ImportLogEntry>();
            }
        }

        /// <summary>
        /// الحصول على إحصائيات الاستيراد
        /// </summary>
        public async Task<ImportStatistics> GetImportStatisticsAsync()
        {
            try
            {
                var logs = await LoadLogsAsync();
                var today = DateTime.Today;
                var thisWeek = today.AddDays(-7);
                var thisMonth = today.AddDays(-30);

                return new ImportStatistics
                {
                    TotalImports = logs.Count,
                    SuccessfulImports = logs.Count(l => l.Status == ImportStatus.Success),
                    FailedImports = logs.Count(l => l.Status == ImportStatus.Failed),
                    TodayImports = logs.Count(l => l.Timestamp.Date == today),
                    WeekImports = logs.Count(l => l.Timestamp >= thisWeek),
                    MonthImports = logs.Count(l => l.Timestamp >= thisMonth),
                    LastImportDate = logs.Any() ? logs.Max(l => l.Timestamp) : (DateTime?)null
                };
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حساب إحصائيات الاستيراد: {ex.Message}");
                return new ImportStatistics();
            }
        }

        /// <summary>
        /// مسح سجل الاستيراد
        /// </summary>
        public async Task ClearLogsAsync()
        {
            try
            {
                await SaveLogsAsync(new List<ImportLogEntry>());
                System.Diagnostics.Debug.WriteLine("✅ تم مسح سجل الاستيراد");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في مسح سجل الاستيراد: {ex.Message}");
            }
        }

        private async Task<List<ImportLogEntry>> LoadLogsAsync()
        {
            try
            {
                if (File.Exists(_logFilePath))
                {
                    var json = await File.ReadAllTextAsync(_logFilePath);
                    return JsonSerializer.Deserialize<List<ImportLogEntry>>(json) ?? new List<ImportLogEntry>();
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحميل سجل الاستيراد: {ex.Message}");
            }

            return new List<ImportLogEntry>();
        }

        private async Task SaveLogsAsync(List<ImportLogEntry> logs)
        {
            try
            {
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    Encoder = System.Text.Encodings.Web.JavaScriptEncoder.UnsafeRelaxedJsonEscaping
                };

                var json = JsonSerializer.Serialize(logs, options);
                await File.WriteAllTextAsync(_logFilePath, json);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ سجل الاستيراد: {ex.Message}");
            }
        }
    }

    /// <summary>
    /// مدخل سجل الاستيراد
    /// </summary>
    public class ImportLogEntry
    {
        public string Id { get; set; } = string.Empty;
        public DateTime Timestamp { get; set; }
        public string FileName { get; set; } = string.Empty;
        public string IndexValue { get; set; } = string.Empty;
        public string VisitNumber { get; set; } = string.Empty;
        public int ProjectsCount { get; set; }
        public int ItineraryDaysCount { get; set; }
        public ImportStatus Status { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public TimeSpan ProcessingTime { get; set; }
    }

    /// <summary>
    /// حالة الاستيراد
    /// </summary>
    public enum ImportStatus
    {
        Success,
        Failed,
        Cancelled
    }

    /// <summary>
    /// إحصائيات الاستيراد
    /// </summary>
    public class ImportStatistics
    {
        public int TotalImports { get; set; }
        public int SuccessfulImports { get; set; }
        public int FailedImports { get; set; }
        public int TodayImports { get; set; }
        public int WeekImports { get; set; }
        public int MonthImports { get; set; }
        public DateTime? LastImportDate { get; set; }
        
        public double SuccessRate => TotalImports > 0 ? (double)SuccessfulImports / TotalImports * 100 : 0;
    }
}
