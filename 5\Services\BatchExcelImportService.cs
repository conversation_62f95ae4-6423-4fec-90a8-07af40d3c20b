using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة استيراد متعدد الملفات من Excel
    /// </summary>
    public class BatchExcelImportService
    {
        private readonly ExcelImportService _excelImportService;
        private readonly ImportLogService _importLogService;

        public BatchExcelImportService()
        {
            _excelImportService = new ExcelImportService();
            _importLogService = new ImportLogService();
        }

        /// <summary>
        /// استيراد متعدد الملفات
        /// </summary>
        public async Task<BatchImportResult> ImportMultipleFilesAsync(string[] filePaths, BatchImportOptions options)
        {
            var result = new BatchImportResult();
            var startTime = DateTime.Now;

            try
            {
                System.Diagnostics.Debug.WriteLine($"🔄 بدء استيراد متعدد الملفات: {filePaths.Length} ملف");

                result.TotalFiles = filePaths.Length;

                foreach (var filePath in filePaths)
                {
                    try
                    {
                        var fileName = Path.GetFileName(filePath);
                        System.Diagnostics.Debug.WriteLine($"📂 معالجة الملف: {fileName}");

                        // استيراد الملف الواحد
                        var fileResult = await ImportSingleFileAsync(filePath, options);
                        result.FileResults.Add(fileResult);

                        if (fileResult.Success)
                        {
                            result.SuccessfulFiles++;
                            System.Diagnostics.Debug.WriteLine($"✅ نجح استيراد: {fileName}");
                        }
                        else
                        {
                            result.FailedFiles++;
                            System.Diagnostics.Debug.WriteLine($"❌ فشل استيراد: {fileName} - {fileResult.ErrorMessage}");
                        }

                        // تحديث التقدم
                        result.ProcessedFiles++;
                        result.ProgressPercentage = (double)result.ProcessedFiles / result.TotalFiles * 100;

                        // إيقاف مؤقت بين الملفات لتجنب الحمل الزائد
                        if (options.DelayBetweenFiles > 0)
                        {
                            await Task.Delay(options.DelayBetweenFiles);
                        }
                    }
                    catch (Exception ex)
                    {
                        result.FailedFiles++;
                        result.ProcessedFiles++;
                        
                        var fileResult = new SingleFileImportResult
                        {
                            FileName = Path.GetFileName(filePath),
                            FilePath = filePath,
                            Success = false,
                            ErrorMessage = ex.Message
                        };
                        result.FileResults.Add(fileResult);

                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في معالجة الملف {filePath}: {ex.Message}");
                    }
                }

                result.ProcessingTime = DateTime.Now - startTime;
                result.Success = result.SuccessfulFiles > 0;

                // تسجيل النتيجة النهائية
                await LogBatchImportResultAsync(result);

                System.Diagnostics.Debug.WriteLine($"✅ انتهى الاستيراد المتعدد - نجح: {result.SuccessfulFiles}, فشل: {result.FailedFiles}");
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
                System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في الاستيراد المتعدد: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// استيراد ملف واحد مع معالجة متقدمة
        /// </summary>
        private async Task<SingleFileImportResult> ImportSingleFileAsync(string filePath, BatchImportOptions options)
        {
            var result = new SingleFileImportResult
            {
                FileName = Path.GetFileName(filePath),
                FilePath = filePath,
                StartTime = DateTime.Now
            };

            try
            {
                // استخراج الرقم المرجعي من اسم الملف أو استخدام الافتراضي
                var indexValue = ExtractIndexFromFileName(filePath, options);
                
                if (string.IsNullOrWhiteSpace(indexValue))
                {
                    result.Success = false;
                    result.ErrorMessage = "لم يتم العثور على رقم مرجعي في اسم الملف";
                    return result;
                }

                // استيراد البيانات
                var importResult = await _excelImportService.ImportFieldVisitFromExcel(filePath, indexValue);
                
                result.Success = importResult.Success;
                result.ErrorMessage = importResult.ErrorMessage;
                result.ImportResult = importResult;
                result.IndexValue = indexValue;

                if (importResult.Success && importResult.VisitData != null)
                {
                    result.VisitNumber = importResult.VisitData.VisitFormNumber;
                    result.ProjectsCount = importResult.Projects.Count;
                    result.ItineraryDaysCount = importResult.Itinerary.Count;
                }
            }
            catch (Exception ex)
            {
                result.Success = false;
                result.ErrorMessage = ex.Message;
            }
            finally
            {
                result.ProcessingTime = DateTime.Now - result.StartTime;
            }

            return result;
        }

        /// <summary>
        /// استخراج الرقم المرجعي من اسم الملف
        /// </summary>
        private string ExtractIndexFromFileName(string filePath, BatchImportOptions options)
        {
            try
            {
                var fileName = Path.GetFileNameWithoutExtension(filePath);
                
                // إذا كان هناك نمط محدد
                if (!string.IsNullOrWhiteSpace(options.IndexPattern))
                {
                    var regex = new System.Text.RegularExpressions.Regex(options.IndexPattern);
                    var match = regex.Match(fileName);
                    if (match.Success && match.Groups.Count > 1)
                    {
                        return match.Groups[1].Value;
                    }
                }

                // البحث عن أرقام في اسم الملف
                var numbers = System.Text.RegularExpressions.Regex.Matches(fileName, @"\d+");
                if (numbers.Count > 0)
                {
                    return numbers[0].Value;
                }

                // استخدام الرقم الافتراضي
                return options.DefaultIndexValue;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استخراج الرقم المرجعي: {ex.Message}");
                return options.DefaultIndexValue;
            }
        }

        /// <summary>
        /// تسجيل نتيجة الاستيراد المتعدد
        /// </summary>
        private async Task LogBatchImportResultAsync(BatchImportResult result)
        {
            try
            {
                foreach (var fileResult in result.FileResults.Where(f => f.Success))
                {
                    var logEntry = new ImportLogEntry
                    {
                        FileName = fileResult.FileName,
                        IndexValue = fileResult.IndexValue,
                        VisitNumber = fileResult.VisitNumber,
                        ProjectsCount = fileResult.ProjectsCount,
                        ItineraryDaysCount = fileResult.ItineraryDaysCount,
                        ProcessingTime = fileResult.ProcessingTime
                    };
                    
                    await _importLogService.LogSuccessfulImportAsync(logEntry);
                }

                foreach (var fileResult in result.FileResults.Where(f => !f.Success))
                {
                    await _importLogService.LogFailedImportAsync(fileResult.FileName, fileResult.ErrorMessage, fileResult.IndexValue);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تسجيل نتائج الاستيراد المتعدد: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على ملفات Excel من مجلد
        /// </summary>
        public string[] GetExcelFilesFromFolder(string folderPath, bool includeSubfolders = false)
        {
            try
            {
                var searchOption = includeSubfolders ? SearchOption.AllDirectories : SearchOption.TopDirectoryOnly;
                var xlsxFiles = Directory.GetFiles(folderPath, "*.xlsx", searchOption);
                var xlsFiles = Directory.GetFiles(folderPath, "*.xls", searchOption);
                
                return xlsxFiles.Concat(xlsFiles).OrderBy(f => f).ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب ملفات Excel: {ex.Message}");
                return Array.Empty<string>();
            }
        }
    }

    /// <summary>
    /// خيارات الاستيراد المتعدد
    /// </summary>
    public class BatchImportOptions
    {
        public string DefaultIndexValue { get; set; } = "1";
        public string IndexPattern { get; set; } = string.Empty; // نمط regex لاستخراج الرقم المرجعي
        public int DelayBetweenFiles { get; set; } = 500; // تأخير بالميلي ثانية
        public bool StopOnFirstError { get; set; } = false;
        public bool ValidateBeforeImport { get; set; } = true;
    }

    /// <summary>
    /// نتيجة الاستيراد المتعدد
    /// </summary>
    public class BatchImportResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public int TotalFiles { get; set; }
        public int ProcessedFiles { get; set; }
        public int SuccessfulFiles { get; set; }
        public int FailedFiles { get; set; }
        public double ProgressPercentage { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public List<SingleFileImportResult> FileResults { get; set; } = new();
        
        public double SuccessRate => TotalFiles > 0 ? (double)SuccessfulFiles / TotalFiles * 100 : 0;
    }

    /// <summary>
    /// نتيجة استيراد ملف واحد
    /// </summary>
    public class SingleFileImportResult
    {
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public string IndexValue { get; set; } = string.Empty;
        public string VisitNumber { get; set; } = string.Empty;
        public int ProjectsCount { get; set; }
        public int ItineraryDaysCount { get; set; }
        public DateTime StartTime { get; set; }
        public TimeSpan ProcessingTime { get; set; }
        public FieldVisitImportResult? ImportResult { get; set; }
    }
}
