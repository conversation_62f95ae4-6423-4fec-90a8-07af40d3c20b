# 🏆 نظام عروض الأسعار للسائقين

## 📋 نظرة عامة

نظام شامل لإدارة عروض الأسعار للسائقين في الزيارات الميدانية، يتيح للمستخدمين:
- تحميل قائمة السائقين المحددين
- إدخال وتعديل عروض الأسعار
- فرز واختيار أفضل العروض
- حفظ النتائج في قاعدة البيانات

## 🚀 المتطلبات

- .NET 9.0 أو أحدث
- Entity Framework Core
- Material Design in XAML
- SQLite Database

## 📁 هيكل المشروع

```
Ali2025   Ali/
├── Models/
│   ├── DriverOffer.cs          # نموذج عرض السائق
│   └── DriverQuote.cs          # نموذج عرض السعر (موجود مسبقاً)
├── ViewModels/
│   └── OffersViewModel.cs      # ViewModel نافذة العروض
├── Views/
│   ├── OffersWindow.xaml       # واجهة نافذة العروض
│   └── OffersWindow.xaml.cs    # Code-behind النافذة
├── Services/
│   ├── IOffersService.cs       # واجهة خدمة العروض
│   ├── OffersService.cs        # تنفيذ خدمة العروض
│   └── SqliteDataService.cs    # خدمة البيانات (محدثة)
└── Examples/
    └── OffersWindowExample.cs  # أمثلة الاستخدام
```

## 🔧 كيفية الاستخدام

### 1. فتح نافذة العروض مع سائقين محددين

```csharp
// الحصول على السائقين المحددين
var selectedDrivers = new ObservableCollection<Driver>();
// ... إضافة السائقين

// فتح نافذة العروض
var offersWindow = new OffersWindow(selectedDrivers, "001", 3);
var result = offersWindow.ShowDialog();

if (result == true)
{
    var selectedOffers = offersWindow.GetSelectedOffers();
    var winner = offersWindow.GetWinnerOffer();
    // معالجة النتائج...
}
```

### 2. فتح نافذة العروض من DataGrid

```csharp
private void OpenOffersButton_Click(object sender, RoutedEventArgs e)
{
    var selectedDrivers = new ObservableCollection<Driver>();
    
    foreach (Driver driver in DriversDataGrid.SelectedItems)
    {
        if (driver.IsActive)
        {
            selectedDrivers.Add(driver);
        }
    }
    
    if (selectedDrivers.Any())
    {
        var visitNumber = VisitNumberTextBox.Text;
        var daysCount = int.Parse(DaysCountTextBox.Text);
        
        var offersWindow = new OffersWindow(selectedDrivers, visitNumber, daysCount);
        offersWindow.ShowDialog();
    }
}
```

### 3. التكامل مع ViewModel

```csharp
public class MainViewModel : BindableBase
{
    public DelegateCommand OpenOffersCommand { get; }
    
    private void ExecuteOpenOffers()
    {
        var selectedDrivers = GetSelectedDrivers();
        OffersWindowExample.OpenOffersWindowWithSelectedDrivers(
            selectedDrivers, CurrentVisitNumber, CurrentDaysCount);
    }
}
```

## 🎯 الميزات الرئيسية

### 1. إدارة العروض
- ✅ تحميل السائقين المحددين تلقائياً
- ✅ إدخال المبالغ المقترحة (قابل للتحرير)
- ✅ حساب المعدل اليومي تلقائياً
- ✅ عرض معلومات السائق والمركبة

### 2. الفرز والاختيار
- 🔄 فرز العروض حسب السعر (تصاعدي)
- 🏆 اختيار أقل عرض تلقائياً
- ✔️ تحديد عروض متعددة
- 🗑️ إلغاء التحديد

### 3. الإحصائيات
- 📊 إجمالي العروض
- ✅ العروض المختارة
- 📈 أقل وأعلى سعر
- 🧮 متوسط الأسعار

### 4. الحفظ والتقارير
- 💾 حفظ العروض في قاعدة البيانات
- 📄 تصدير النتائج كنص
- 🔍 البحث في العروض المحفوظة

## 🎨 التصميم

### الألوان المستخدمة
- **الرأس**: تدرج أزرق-بنفسجي (#6A5ACD → #4169E1)
- **الأزرار الإيجابية**: أخضر (#4CAF50)
- **الأزرار الثانوية**: رمادي (#9E9E9E)
- **التحذيرات**: برتقالي (#FF9800)
- **الأخطاء**: أحمر (#F44336)

### العناصر المرئية
- 🏆 أيقونة الفائز
- 📊 بطاقات الإحصائيات
- 🔄 مؤشر التحميل
- ✔️ صناديق الاختيار

## 📊 قاعدة البيانات

### الجداول المستخدمة
1. **DriverQuotes**: حفظ تفاصيل العروض
2. **FieldVisits**: ربط العروض بالزيارات
3. **Drivers**: معلومات السائقين

### حقول العروض
```sql
-- في جدول DriverQuotes
QuotedPrice DECIMAL(18,2)  -- المبلغ المقترح
QuotedDays INTEGER         -- عدد الأيام
Status INTEGER             -- حالة العرض (Pending/Approved/Rejected)
Notes TEXT                 -- ملاحظات (تحتوي رقم الزيارة)
```

## 🔧 التخصيص

### تعديل عدد الأيام الافتراضي
```csharp
// في OffersViewModel.cs
private int _visitDaysCount = 3; // تغيير القيمة الافتراضية
```

### تخصيص رسائل التحقق
```csharp
// في DriverOffer.cs
public bool IsValid()
{
    return !string.IsNullOrEmpty(DriverName) && 
           ProposedAmount > 0 && 
           DaysCount > 0;
}
```

### إضافة حقول جديدة
```csharp
// في DriverOffer.cs
public string CustomField { get; set; } = string.Empty;

// في OffersWindow.xaml
<DataGridTextColumn Header="حقل مخصص" Binding="{Binding CustomField}"/>
```

## 🐛 استكشاف الأخطاء

### مشاكل شائعة وحلولها

1. **لا تظهر البيانات في DataGrid**
   ```csharp
   // تأكد من تحديث ObservableCollection
   DriverOffers.Clear();
   foreach (var offer in newOffers)
   {
       DriverOffers.Add(offer);
   }
   ```

2. **لا يتم حساب المعدل اليومي**
   ```csharp
   // تأكد من استدعاء OnPropertyChanged
   OnPropertyChanged(nameof(DailyRate));
   ```

3. **خطأ في حفظ البيانات**
   ```csharp
   // تحقق من صحة البيانات قبل الحفظ
   if (!offer.IsValid())
   {
       ShowError("بيانات العرض غير صحيحة");
       return;
   }
   ```

## 📝 ملاحظات التطوير

### إضافات مستقبلية
- [ ] تصدير العروض إلى Excel
- [ ] إرسال العروض عبر البريد الإلكتروني
- [ ] تقارير مفصلة للعروض
- [ ] مقارنة العروض بصرياً
- [ ] حفظ قوالب العروض

### تحسينات الأداء
- استخدام `async/await` في جميع العمليات
- تحسين استعلامات قاعدة البيانات
- إضافة فهارس للبحث السريع

## 📞 الدعم

للحصول على المساعدة أو الإبلاغ عن مشاكل:
- 📧 البريد الإلكتروني: <EMAIL>
- 📱 الهاتف: +966-XX-XXX-XXXX
- 🌐 الموقع: www.drivermanagement.com

---

**تم تطوير النظام بواسطة فريق إدارة السائقين © 2024**
