using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Windows;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.ViewModels
{
    public class SectorManagementViewModel : BindableBase
    {
        private readonly IDataService _dataService;
        private Sector? _selectedSector;
        private Officer? _selectedOfficer;
        private string _officerName = string.Empty;
        private string _selectedRank = string.Empty;
        private string _phoneNumber = string.Empty;
        private string _cardNumber = string.Empty;
        private string _selectedCardType = string.Empty;
        private string _code = string.Empty;

        public SectorManagementViewModel()
        {
            _dataService = new DataService();
            
            // Initialize collections
            Sectors = new ObservableCollection<Sector>();
            Officers = new ObservableCollection<Officer>();
            
            // Initialize dropdown data
            Ranks = new ObservableCollection<string>
            {
                "محاسب المشروع",
                "ضابط مشاريع",
                "ضابط مجتمعي",
                "ضابط محاسبي",
                "ضابط مالي",
                "ضابط الشكاوى",
                "ضابط التعاقدات",
                "ضابط الفنية",
                "ضابط المراقبة والتقييم",
                "سكرتاريه التغذية",
                "نائب مدير الفرع",
                "استشاري",
                "محاسب",
                "مهندس",
                "فني",
                "إداري",
                "مشرف"
            };
            
            CardTypes = new ObservableCollection<string>
            {
                "شخصية",
                "عسكرية",
                "مدنية",
                "دبلوماسية"
            };

            // Initialize commands
            AddCommand = new DelegateCommand(AddOfficer, CanExecuteAdd);
            SaveCommand = new DelegateCommand(SaveOfficer, CanExecuteSave);
            DeleteCommand = new DelegateCommand(DeleteOfficer, CanExecuteDelete);
            PreviousCommand = new DelegateCommand(PreviousRecord);
            NextCommand = new DelegateCommand(NextRecord);

            // Load initial data
            LoadData();
        }

        #region Properties

        public ObservableCollection<Sector> Sectors { get; }
        public ObservableCollection<Officer> Officers { get; }
        public ObservableCollection<string> Ranks { get; }
        public ObservableCollection<string> CardTypes { get; }

        public Sector? SelectedSector
        {
            get => _selectedSector;
            set
            {
                SetProperty(ref _selectedSector, value);
                LoadOfficersForSector();
            }
        }

        public Officer? SelectedOfficer
        {
            get => _selectedOfficer;
            set
            {
                SetProperty(ref _selectedOfficer, value);
                LoadOfficerDetails();
                RaiseCanExecuteChanged();
            }
        }

        public string OfficerName
        {
            get => _officerName;
            set
            {
                SetProperty(ref _officerName, value);
                RaiseCanExecuteChanged();
            }
        }

        public string SelectedRank
        {
            get => _selectedRank;
            set => SetProperty(ref _selectedRank, value);
        }

        public string PhoneNumber
        {
            get => _phoneNumber;
            set => SetProperty(ref _phoneNumber, value);
        }

        public string CardNumber
        {
            get => _cardNumber;
            set => SetProperty(ref _cardNumber, value);
        }

        public string SelectedCardType
        {
            get => _selectedCardType;
            set => SetProperty(ref _selectedCardType, value);
        }

        public string Code
        {
            get => _code;
            set => SetProperty(ref _code, value);
        }

        #endregion

        #region Commands

        public DelegateCommand AddCommand { get; }
        public DelegateCommand SaveCommand { get; }
        public DelegateCommand DeleteCommand { get; }
        public DelegateCommand PreviousCommand { get; }
        public DelegateCommand NextCommand { get; }

        #endregion

        #region Command Methods

        private async void AddOfficer()
        {
            try
            {
                if (SelectedSector == null)
                {
                    MessageBox.Show("يرجى اختيار القطاع أولاً", "تنبيه", MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                var newOfficer = new Officer
                {
                    Name = OfficerName,
                    Rank = SelectedRank,
                    PhoneNumber = PhoneNumber,
                    CardNumber = CardNumber,
                    CardType = SelectedCardType,
                    Code = Code,
                    SectorId = SelectedSector.Id,
                    SectorName = SelectedSector.Name
                };

                // حفظ في قاعدة البيانات
                var success = await _dataService.AddOfficerAsync(newOfficer);

                if (success)
                {
                    Officers.Add(newOfficer);
                    ClearForm();
                    MessageBox.Show("تم إضافة الضابط بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في إضافة الضابط", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة الضابط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveOfficer()
        {
            try
            {
                if (SelectedOfficer == null) return;

                SelectedOfficer.Name = OfficerName;
                SelectedOfficer.Rank = SelectedRank;
                SelectedOfficer.PhoneNumber = PhoneNumber;
                SelectedOfficer.CardNumber = CardNumber;
                SelectedOfficer.CardType = SelectedCardType;
                SelectedOfficer.Code = Code;

                // تحديث في قاعدة البيانات
                var success = await _dataService.UpdateOfficerAsync(SelectedOfficer);

                if (success)
                {
                    MessageBox.Show("تم حفظ التعديلات بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التعديلات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteOfficer()
        {
            try
            {
                if (SelectedOfficer == null) return;

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف الضابط: {SelectedOfficer.Name}؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // حذف من قاعدة البيانات
                    var success = await _dataService.DeleteOfficerAsync(SelectedOfficer.Id);

                    if (success)
                    {
                        Officers.Remove(SelectedOfficer);
                        ClearForm();
                        MessageBox.Show("تم حذف الضابط بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف الضابط", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف الضابط: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PreviousRecord()
        {
            if (Officers.Count == 0) return;
            
            var currentIndex = SelectedOfficer != null ? Officers.IndexOf(SelectedOfficer) : -1;
            var previousIndex = currentIndex > 0 ? currentIndex - 1 : Officers.Count - 1;
            SelectedOfficer = Officers[previousIndex];
        }

        private void NextRecord()
        {
            if (Officers.Count == 0) return;
            
            var currentIndex = SelectedOfficer != null ? Officers.IndexOf(SelectedOfficer) : -1;
            var nextIndex = currentIndex < Officers.Count - 1 ? currentIndex + 1 : 0;
            SelectedOfficer = Officers[nextIndex];
        }

        #endregion

        #region Can Execute Methods

        private bool CanExecuteAdd()
        {
            return !string.IsNullOrWhiteSpace(OfficerName) && SelectedSector != null;
        }

        private bool CanExecuteSave()
        {
            return SelectedOfficer != null && !string.IsNullOrWhiteSpace(OfficerName);
        }

        private bool CanExecuteDelete()
        {
            return SelectedOfficer != null;
        }

        #endregion

        #region Helper Methods

        private async void LoadData()
        {
            try
            {
                var sectors = await _dataService.GetSectorsAsync();
                Sectors.Clear();
                foreach (var sector in sectors)
                {
                    Sectors.Add(sector);
                }

                if (Sectors.Any())
                {
                    SelectedSector = Sectors.First();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void LoadOfficersForSector()
        {
            Officers.Clear();

            if (SelectedSector == null) return;

            try
            {
                // تحميل جميع الضباط من قاعدة البيانات
                var allOfficers = await _dataService.GetOfficersAsync();

                // تصفية الضباط حسب القطاع المختار
                var sectorOfficers = allOfficers.Where(o => o.SectorId == SelectedSector.Id).ToList();

                foreach (var officer in sectorOfficers)
                {
                    Officers.Add(officer);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل ضباط القطاع: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadOfficerDetails()
        {
            if (SelectedOfficer == null)
            {
                ClearForm();
                return;
            }

            OfficerName = SelectedOfficer.Name;
            SelectedRank = SelectedOfficer.Rank;
            PhoneNumber = SelectedOfficer.PhoneNumber;
            CardNumber = SelectedOfficer.CardNumber;
            SelectedCardType = SelectedOfficer.CardType;
            Code = SelectedOfficer.Code;
        }

        private void ClearForm()
        {
            OfficerName = string.Empty;
            SelectedRank = string.Empty;
            PhoneNumber = string.Empty;
            CardNumber = string.Empty;
            SelectedCardType = string.Empty;
            Code = string.Empty;
        }

        private void RaiseCanExecuteChanged()
        {
            AddCommand.RaiseCanExecuteChanged();
            SaveCommand.RaiseCanExecuteChanged();
            DeleteCommand.RaiseCanExecuteChanged();
        }

        #endregion
    }
}
