<Window x:Class="DriverManagementSystem.Views.BatchImportWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="📂 استيراد متعدد الملفات من Excel" Height="700" Width="900"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Modern Button Style like Excel Import with Icons -->
        <Style x:Key="FormalButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}"
                               x:Name="border">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock x:Name="IconText" Text="{TemplateBinding Tag}"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="{TemplateBinding Foreground}"
                                          Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="Border" x:Key="SectionStyle">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Padding="20" Margin="0,0,0,15">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667eea" Offset="0"/>
                    <GradientStop Color="#764ba2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel>
                <TextBlock Text="📂 استيراد متعدد الملفات من Excel" 
                          FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                <TextBlock Text="استيراد عدة ملفات Excel دفعة واحدة بكفاءة عالية" 
                          FontSize="12" Foreground="#E8E8E8" HorizontalAlignment="Center" Margin="0,5,0,0"/>
            </StackPanel>
        </Border>

        <!-- File Selection -->
        <Border Grid.Row="1" Style="{StaticResource SectionStyle}" Margin="20,0">
            <StackPanel>
                <TextBlock Text="📁 اختيار الملفات" FontSize="16" FontWeight="Bold" Margin="0,0,0,15"/>
                
                <Grid>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="Auto"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    
                    <Button Grid.Column="0" Content="اختيار ملفات متعددة" Tag="📁"
                           Background="#4CAF50" Foreground="White"
                           Padding="15,10" FontWeight="Bold" Margin="0,0,10,0"
                           Click="SelectFilesButton_Click"
                           Style="{StaticResource FormalButtonStyle}"/>

                    <Button Grid.Column="1" Content="اختيار ملفات إضافية" Tag="📂"
                           Background="#2196F3" Foreground="White"
                           Padding="15,10" FontWeight="Bold" Margin="0,0,10,0"
                           Click="SelectFolderButton_Click"
                           Style="{StaticResource FormalButtonStyle}"/>

                    <Button Grid.Column="2" Content="مسح القائمة" Tag="🗑"
                           Background="#F44336" Foreground="White"
                           Padding="15,10" FontWeight="Bold"
                           Click="ClearListButton_Click"
                           Style="{StaticResource FormalButtonStyle}"/>
                </Grid>

                <!-- Options -->
                <Expander Header="⚙️ خيارات متقدمة" Margin="0,15,0,0" IsExpanded="False">
                    <Grid Margin="0,10,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>
                        
                        <StackPanel Grid.Column="0" Margin="0,0,10,0">
                            <TextBlock Text="الرقم المرجعي الافتراضي:" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Name="DefaultIndexTextBox" Text="1" Padding="8" FontSize="12"/>
                            
                            <TextBlock Text="نمط استخراج الرقم المرجعي:" FontWeight="Bold" Margin="0,10,0,5"/>
                            <TextBox Name="IndexPatternTextBox" Padding="8" FontSize="12" 
                                    ToolTip="مثال: (\d+) لاستخراج أول رقم من اسم الملف"/>
                        </StackPanel>
                        
                        <StackPanel Grid.Column="1" Margin="10,0,0,0">
                            <TextBlock Text="تأخير بين الملفات (مللي ثانية):" FontWeight="Bold" Margin="0,0,0,5"/>
                            <TextBox Name="DelayTextBox" Text="500" Padding="8" FontSize="12"/>
                            
                            <CheckBox Name="IncludeSubfoldersCheckBox" Content="تضمين المجلدات الفرعية" 
                                     Margin="0,15,0,0" FontWeight="Bold"/>
                            <CheckBox Name="StopOnErrorCheckBox" Content="إيقاف عند أول خطأ" 
                                     Margin="0,5,0,0" FontWeight="Bold"/>
                        </StackPanel>
                    </Grid>
                </Expander>
            </StackPanel>
        </Border>

        <!-- Files List -->
        <Border Grid.Row="2" Style="{StaticResource SectionStyle}" Margin="20,0">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="📋 قائمة الملفات المحددة" FontSize="16" FontWeight="Bold"/>
                    <TextBlock Text="{Binding SelectedFilesCount, StringFormat=({0} ملف)}" 
                              FontSize="14" Foreground="#666" Margin="10,0,0,0"/>
                </StackPanel>

                <DataGrid Grid.Row="1" Name="FilesDataGrid" 
                         ItemsSource="{Binding SelectedFiles}"
                         AutoGenerateColumns="False" 
                         CanUserAddRows="False"
                         CanUserDeleteRows="True"
                         GridLinesVisibility="Horizontal"
                         HeadersVisibility="Column"
                         AlternatingRowBackground="#F8F9FA"
                         MaxHeight="300">
                    
                    <DataGrid.Columns>
                        <DataGridTextColumn Header="اسم الملف" Binding="{Binding FileName}" Width="200"/>
                        <DataGridTextColumn Header="المسار" Binding="{Binding FilePath}" Width="*"/>
                        <DataGridTextColumn Header="الحجم" Binding="{Binding FileSize}" Width="80"/>
                        <DataGridTextColumn Header="تاريخ التعديل" Binding="{Binding LastModified, StringFormat=dd/MM/yyyy}" Width="100"/>
                    </DataGrid.Columns>
                </DataGrid>
            </Grid>
        </Border>

        <!-- Progress -->
        <Border Grid.Row="3" Style="{StaticResource SectionStyle}" Margin="20,0" 
               Visibility="{Binding ProgressVisibility}">
            <StackPanel>
                <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                    <TextBlock Text="⏳ تقدم الاستيراد" FontSize="16" FontWeight="Bold"/>
                    <TextBlock Text="{Binding ProgressText}" FontSize="14" Foreground="#666" Margin="10,0,0,0"/>
                </StackPanel>
                
                <ProgressBar Value="{Binding ProgressPercentage}" Maximum="100" Height="25" 
                           Background="#E0E0E0" Foreground="#4CAF50"/>
                
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                    <TextBlock Text="نجح:" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding SuccessfulFiles}" Foreground="#4CAF50" Margin="0,0,20,0"/>
                    <TextBlock Text="فشل:" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding FailedFiles}" Foreground="#F44336" Margin="0,0,20,0"/>
                    <TextBlock Text="المتبقي:" FontWeight="Bold" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding RemainingFiles}" Foreground="#666"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Buttons -->
        <Border Grid.Row="4" Background="#F5F5F5" Padding="20">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                <Button Name="StartImportButton" Content="بدء الاستيراد" Tag="▶"
                        Background="#4CAF50" Foreground="White"
                        Padding="25,12" Margin="0,0,15,0"
                        Click="StartImportButton_Click"
                        IsEnabled="{Binding CanStartImport}"
                        Style="{StaticResource FormalButtonStyle}"/>

                <Button Name="StopImportButton" Content="إيقاف" Tag="⏹"
                        Background="#FF9800" Foreground="White"
                        Padding="25,12" Margin="0,0,15,0"
                        Click="StopImportButton_Click"
                        IsEnabled="{Binding CanStopImport}"
                        Style="{StaticResource FormalButtonStyle}"/>

                <Button Name="ViewResultsButton" Content="عرض النتائج" Tag="📊"
                        Background="#2196F3" Foreground="White"
                        Padding="25,12" Margin="0,0,15,0"
                        Click="ViewResultsButton_Click"
                        IsEnabled="{Binding CanViewResults}"
                        Style="{StaticResource FormalButtonStyle}"/>

                <Button Name="CloseButton" Content="إغلاق" Tag="✕"
                        Background="#F44336" Foreground="White"
                        Padding="25,12"
                        Click="CloseButton_Click"
                        Style="{StaticResource FormalButtonStyle}"/>
            </StackPanel>
        </Border>

    </Grid>
</Window>
