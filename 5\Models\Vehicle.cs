using System;

namespace DriverManagementSystem.Models
{
    public class Vehicle
    {
        public int Id { get; set; }
        public string Code { get; set; } = string.Empty;
        public string PlateNumber { get; set; } = string.Empty;
        public string Brand { get; set; } = string.Empty;
        public string Model { get; set; } = string.Empty;
        public int Year { get; set; }
        public string Color { get; set; } = string.Empty;
        public int SectorId { get; set; }
        public string SectorName { get; set; } = string.Empty;

        // Driver Information
        public string OwnerName { get; set; } = string.Empty;
        public string IdCardNumber { get; set; } = string.Empty;
        public string LicenseNumber { get; set; } = string.Empty;
        public string VehicleType { get; set; } = string.Empty;
        public string DriverCode { get; set; } = string.Empty;

        public DateTime CreatedAt { get; set; } = DateTime.Now;
        public bool IsActive { get; set; } = true;
    }
}
