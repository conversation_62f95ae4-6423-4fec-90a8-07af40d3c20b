<UserControl x:Class="DriverManagementSystem.Views.SimpleUserManagementView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             Background="#F8F9FA"
             FlowDirection="RightToLeft">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Background="#2196F3" Padding="20">
            <StackPanel Orientation="Horizontal">
                <TextBlock Text="👥" FontSize="24" Foreground="White" Margin="0,0,15,0" VerticalAlignment="Center"/>
                <TextBlock Text="إدارة المستخدمين" 
                         FontSize="20" 
                         FontWeight="Bold" 
                         Foreground="White"
                         VerticalAlignment="Center"/>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Padding="20">
            <StackPanel>
                <!-- Add User Button -->
                <Button x:Name="AddUserButton" 
                        Content="➕ إضافة مستخدم جديد"
                        Background="#4CAF50"
                        Foreground="White"
                        FontSize="16"
                        FontWeight="Bold"
                        Height="50"
                        Margin="0,0,0,20"
                        BorderThickness="0"
                        Cursor="Hand"
                        Click="AddUserButton_Click"/>

                <!-- Users List -->
                <Border Background="White" 
                        CornerRadius="8" 
                        Padding="20"
                        BorderBrush="#E0E0E0" 
                        BorderThickness="1">
                    <StackPanel x:Name="UsersPanel">
                        <TextBlock Text="جاري تحميل المستخدمين..." 
                                 FontSize="16" 
                                 HorizontalAlignment="Center"
                                 Margin="20"/>
                    </StackPanel>
                </Border>
            </StackPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
