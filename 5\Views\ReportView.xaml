<UserControl x:Class="DriverManagementSystem.Views.ReportView"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             FlowDirection="RightToLeft"
             Background="White">

    <UserControl.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="../Styles/PrintStyles.xaml"/>
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>
    </UserControl.Resources>

    <!-- A4 Report Container - All Pages in One View -->
    <ScrollViewer VerticalScrollBarVisibility="Auto" HorizontalScrollBarVisibility="Auto" Background="White">
        <StackPanel Background="White" HorizontalAlignment="Center">

        <!-- الصفحة الأولى: محضر استخراج عروض الأسعار -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                <StackPanel Margin="5">

                <!-- Compact Professional Header -->
                <Grid Margin="0,0,0,10">
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="*"/>
                        <ColumnDefinition Width="2*"/>
                        <ColumnDefinition Width="*"/>
                    </Grid.ColumnDefinitions>

                    <!-- Left Side - Organization Header -->
                    <StackPanel Grid.Column="0" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <TextBlock Text="الجمهورية اليمنية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                        <TextBlock Text="الصندوق الاجتماعي للتنمية" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#1E3A8A" Margin="0,2,0,0"/>
                        <TextBlock Text="فرع ذمار والبيضاء" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                    </StackPanel>

                    <!-- Center - Main Title -->
                    <Border Grid.Column="1" Background="#E8F4FD" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="10" Padding="20,10" HorizontalAlignment="Center">
                        <TextBlock Text="محضر استدراج عروض اسعار" FontSize="20" FontWeight="Bold"
                                 HorizontalAlignment="Center" Foreground="#1E3A8A" TextAlignment="Center"/>
                    </Border>

                    <!-- Right Side - Date and Visit Number with Icons -->
                    <StackPanel Grid.Column="2" HorizontalAlignment="Center" VerticalAlignment="Center">
                        <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="1" Padding="8,4" Margin="0,0,0,4" CornerRadius="5">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="📅" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                    <Run Text="التاريخ: "/>
                                    <Run Text="{Binding ReportData.ReportDate, FallbackValue='15/06/2025'}"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                        <Border Background="#F0FFF0" BorderBrush="#228B22" BorderThickness="1" Padding="8,4" CornerRadius="5">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                <TextBlock Text="🔢" FontSize="12" Margin="0,0,5,0" VerticalAlignment="Center"/>
                                <TextBlock FontSize="10" FontWeight="Bold" Foreground="#333333" VerticalAlignment="Center">
                                    <Run Text="رقم الزيارة: "/>
                                    <Run Text="{Binding ReportData.VisitNumber, FallbackValue='911-13031'}"/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>
                </Grid>

                    <!-- Compact Projects Section -->
                    <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,10" Background="White">
                        <StackPanel>
                            <!-- Compact Header with Sector -->
                            <Border Background="#F8F9FA" Padding="8,6">
                                <Grid>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="Auto"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Projects Title on the left -->
                                    <StackPanel Grid.Column="0" HorizontalAlignment="Left">
                                        <TextBlock Text="المشاريع التي سيتم زيارتها"
                                                 FontWeight="Bold" FontSize="12" HorizontalAlignment="Left" Foreground="Black"/>
                                    </StackPanel>

                                    <!-- Empty space in center -->
                                    <TextBlock Grid.Column="1"/>

                                    <!-- Sector on the right with Icon -->
                                    <Border Grid.Column="2" BorderBrush="#DDDDDD" BorderThickness="1" Padding="8,5" Background="White">
                                        <TextBlock HorizontalAlignment="Center" VerticalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black">
                                            <Run Text="🏢 القطاع: "/>
                                            <Run Text="{Binding ReportData.SectorName, FallbackValue='الصحة والحماية الاجتماعية'}"/>
                                        </TextBlock>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Enhanced Table Header -->
                            <Border BorderBrush="Black" BorderThickness="2" Background="#E8F4FD">
                                <Grid MinHeight="35">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="80"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                        <TextBlock Text="رقم المشروع" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,8">
                                        <TextBlock Text="اسم المشروع" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="2" Padding="8,8">
                                        <TextBlock Text="عدد الأيام" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Enhanced Table Data -->
                            <ItemsControl ItemsSource="{Binding ReportData.Projects}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="Black" BorderThickness="2,0,2,1">
                                            <Grid MinHeight="35" Background="White">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="120"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="80"/>
                                                </Grid.ColumnDefinitions>

                                                <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                    <TextBlock Text="{Binding ProjectNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="SemiBold" Foreground="Black"/>
                                                </Border>
                                                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="8,6">
                                                    <TextBlock Text="{Binding ProjectName}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" TextWrapping="Wrap"
                                                             FontSize="11" LineHeight="16" Foreground="Black"/>
                                                </Border>
                                                <Border Grid.Column="2" Padding="8,6">
                                                    <TextBlock Text="{Binding SerialNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="11" FontWeight="Bold" Foreground="Black"/>
                                                </Border>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>

                    <!-- Compact Visit Data Section -->
                    <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,10" Background="White">
                        <StackPanel>
                            <!-- Compact Header -->
                            <Border Background="#4682B4" Padding="8,6">
                                <TextBlock Text="بيانات الزيارة الميدانية" FontWeight="Bold" FontSize="11"
                                         Foreground="White" HorizontalAlignment="Center"/>
                            </Border>

                            <!-- Compact Content -->
                            <StackPanel Margin="10,8">
                                <!-- Activity Nature -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,12">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🎯" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Top" MaxWidth="600">
                                            <Run Text="طبيعة النشاط: " FontWeight="Bold" Foreground="Black"/>
                                            <Run Text="{Binding ReportData.VisitNature, FallbackValue='اضافة نشاط المهمة'}" Foreground="Black"/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <!-- Visit Conductor -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,10">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="👤" FontSize="14" Margin="0,0,10,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Top" MaxWidth="600">
                                            <Run Text="القائم بالزيارة: " FontWeight="Bold" Foreground="Black"/>
                                            <Run Text="{Binding ReportData.VisitConductor, FallbackValue=' '}" Foreground="Black"/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>



                                <!-- Route and Message Frame -->
                                <Border Margin="0,5,0,5" BorderBrush="Black" BorderThickness="2" Background="White">
                                    <StackPanel>
                                        <!-- Message Header -->
                                        <Border Background="#E8F4FD" Padding="8,6">
                                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Left">
                                                <TextBlock Text="🗺️" FontSize="12" Margin="0,0,8,0" Foreground="Black" VerticalAlignment="Center"/>
                                                <TextBlock Text="خط السير ونص الرسالة المرسلة للسائقين" FontSize="12" FontWeight="Bold"
                                                         Foreground="Black" VerticalAlignment="Center"/>
                                            </StackPanel>
                                        </Border>

                                        <!-- Message Content -->
                                        <Border Background="White" Padding="10,8">
                                            <Border BorderBrush="#CCCCCC" BorderThickness="1" Padding="8" Background="White">
                                                <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="16" Foreground="Black"
                                                         Text="{Binding ReportData.WinnerDriverMessage, FallbackValue='لم يتم حفظ نص الرسالة للسائق الفائز'}"
                                                         TextAlignment="Left" FontFamily="Segoe UI"/>
                                            </Border>
                                        </Border>
                                    </StackPanel>
                                </Border>

                                <!-- Enhanced Dates Section in Single Row -->
                                <Border Background="#F8F9FA" BorderBrush="#DEE2E6" BorderThickness="1" CornerRadius="8" Padding="15,12" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <!-- Departure Date -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                                            <TextBlock Text="📅" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="تاريخ النزول:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                            <TextBlock Text="{Binding ReportData.DepartureDate, FallbackValue='15/06/2025'}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Return Date -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,30,0">
                                            <TextBlock Text="🔄" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="تاريخ العودة:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                            <TextBlock Text="{Binding ReportData.ReturnDate, FallbackValue='17/06/2025'}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Days Count -->
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="⏰" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="عدد الأيام:" FontWeight="Bold" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="0,0,5,0"/>
                                            <TextBlock Text="{Binding ReportData.DaysCount, FallbackValue='3'}"
                                                     FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                            <TextBlock Text=" يوم" FontSize="12" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>

                                <!-- Simplified Notes Section -->
                                <StackPanel Orientation="Horizontal" Margin="0,8,0,10">
                                    <TextBlock Text="📝" FontSize="14" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="18" VerticalAlignment="Center" MaxWidth="600">
                                        <Run Text="ملاحظات: " FontWeight="Bold" Foreground="Black"/>
                                        <Run Text="{Binding ReportData.Notes, FallbackValue='لا توجد ملاحظات إضافية'}" Foreground="Black"/>
                                    </TextBlock>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Compact Price Offers Table -->
                    <Border BorderBrush="#333333" BorderThickness="1" Margin="0,0,0,0" Background="White">
                        <StackPanel>
                            <!-- Enhanced Header -->
                            <Border Background="#B8D4F0" Padding="10,8">
                                <TextBlock Text="قائمة الأسعار المقدمة من السائقين" FontWeight="Bold" FontSize="14"
                                         Foreground="Black" HorizontalAlignment="Center"/>
                            </Border>

                            <!-- Enhanced Table Header -->
                            <Border BorderBrush="Black" BorderThickness="2" Background="#F0F8FF">
                                <Grid MinHeight="35">
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="60"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="120"/>
                                        <ColumnDefinition Width="100"/>
                                        <ColumnDefinition Width="80"/>
                                    </Grid.ColumnDefinitions>

                                    <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                        <TextBlock Text="الرقم" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                        <TextBlock Text="اسم السائق" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                        <TextBlock Text="رقم التلفون" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                        <TextBlock Text="السعر المقدم" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                    <Border Grid.Column="4" Padding="10">
                                        <TextBlock Text="الحالة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" VerticalAlignment="Center" Foreground="Black"/>
                                    </Border>
                                </Grid>
                            </Border>

                            <!-- Enhanced Table Data -->
                            <ItemsControl ItemsSource="{Binding ReportData.PriceOffers}">
                                <ItemsControl.ItemTemplate>
                                    <DataTemplate>
                                        <Border BorderBrush="Black" BorderThickness="2,0,2,1">
                                            <Grid MinHeight="35" Background="White">
                                                <Grid.ColumnDefinitions>
                                                    <ColumnDefinition Width="60"/>
                                                    <ColumnDefinition Width="*"/>
                                                    <ColumnDefinition Width="120"/>
                                                    <ColumnDefinition Width="100"/>
                                                    <ColumnDefinition Width="80"/>
                                                </Grid.ColumnDefinitions>

                                                <!-- Serial Number -->
                                                <Border Grid.Column="0" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                    <TextBlock Text="{Binding SerialNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="13" FontWeight="Bold" Foreground="Black"/>
                                                </Border>

                                                <!-- Driver Name -->
                                                <Border Grid.Column="1" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                    <TextBlock Text="{Binding DriverName}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="13" FontWeight="SemiBold" Foreground="Black"/>
                                                </Border>

                                                <!-- Phone Number -->
                                                <Border Grid.Column="2" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                    <TextBlock Text="{Binding PhoneNumber}" HorizontalAlignment="Center"
                                                             VerticalAlignment="Center" FontSize="12" FontFamily="Consolas" Foreground="Black"/>
                                                </Border>

                                                <!-- Price -->
                                                <Border Grid.Column="3" BorderBrush="Black" BorderThickness="0,0,1,0" Padding="10">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <TextBlock Text="{Binding OfferedPrice, StringFormat='{}{0:N0}'}"
                                                                 FontSize="13" FontWeight="Bold" Foreground="Black" VerticalAlignment="Center"/>
                                                        <TextBlock Text=" ريال" FontSize="12" Foreground="Black" VerticalAlignment="Center" Margin="2,0,0,0"/>
                                                    </StackPanel>
                                                </Border>

                                                <!-- Status -->
                                                <Border Grid.Column="4" Padding="10">
                                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                                        <TextBlock Text="{Binding Status}" FontSize="12" FontWeight="Bold" VerticalAlignment="Center" Foreground="Black"/>
                                                        <TextBlock Text=" 🏆" FontSize="14" VerticalAlignment="Center" Margin="3,0,0,0">
                                                            <TextBlock.Style>
                                                                <Style TargetType="TextBlock">
                                                                    <Setter Property="Visibility" Value="Collapsed"/>
                                                                    <Style.Triggers>
                                                                        <DataTrigger Binding="{Binding IsWinner}" Value="True">
                                                                            <Setter Property="Visibility" Value="Visible"/>
                                                                        </DataTrigger>
                                                                    </Style.Triggers>
                                                                </Style>
                                                            </TextBlock.Style>
                                                        </TextBlock>
                                                    </StackPanel>
                                                </Border>
                                            </Grid>
                                        </Border>
                                    </DataTemplate>
                                </ItemsControl.ItemTemplate>
                            </ItemsControl>
                        </StackPanel>
                    </Border>

                    <!-- Simplified Award Decision Section -->
                    <StackPanel Margin="0,0,0,5">
                        <!-- Decision Text and Driver Details in Single Section -->
                        <Border Background="White" Padding="10,5" Margin="0,0,0,0">
                            <StackPanel>
                                <!-- Decision Text in Single Line with Driver Name - No Wrapping -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Right" Margin="0,0,0,5">
                                    <TextBlock Text="بناءً على الأسعار المقدمة من الإخوة أصحاب المركبات الذي تم التواصل معهم أعلاه، فقد تم إرساء النقل على الأخ" FontSize="13" Foreground="Black" VerticalAlignment="Center"/>
                                    <TextBlock Text=" " FontSize="13" Foreground="Black" VerticalAlignment="Center"/>
                                    <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" FontWeight="Bold" FontSize="14" Foreground="Black" VerticalAlignment="Center"/>
                                </StackPanel>

                                <!-- Winner Driver Details in Distinguished Frame - Same Line -->
                                <Border Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="8" Padding="15,10" Margin="0,0,0,0">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <!-- ID -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                            <TextBlock Text="🆔" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="رقم البطاقة:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.NationalId}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Vehicle Type -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                            <TextBlock Text="🚙" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="نوع السيارة:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.VehicleType}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Capacity -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,25,0">
                                            <TextBlock Text="⚖️" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="قدرة السيارة:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.VehicleCapacity}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>

                                        <!-- Year -->
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="📅" FontSize="16" Margin="0,0,6,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="سنة الصنع:" FontWeight="Bold" FontSize="13" Foreground="Black" VerticalAlignment="Center" Margin="0,0,4,0"/>
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.ManufactureYear}" FontSize="13" FontWeight="Bold"
                                                     Foreground="Black" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </Border>
                    </StackPanel>


                    <!-- Professional Signatures Section -->
                    <Border Background="White" Padding="15,12" Margin="0,20,0,8">
                        <StackPanel>
                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Task Manager -->
                                <StackPanel Grid.Column="0" HorizontalAlignment="Center" Margin="15">
                                    <TextBlock Text="المكلف بالمهمة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                    <TextBlock HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" TextWrapping="Wrap" MaxWidth="180" Foreground="Black" LineHeight="18"
                                             Text="{Binding ReportData.VisitConductorFormatted, Mode=OneWay, FallbackValue='جمال علي عبدالله الفاطمي &amp; أحمد صالح أحمد حميد'}"/>
                                </StackPanel>

                                <!-- Movement Responsible -->
                                <StackPanel Grid.Column="1" HorizontalAlignment="Center" Margin="15">
                                    <TextBlock Text="مسئول الحركة" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                    <TextBlock Text="علي علي العمدي" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                </StackPanel>

                                <!-- Branch Manager -->
                                <StackPanel Grid.Column="2" HorizontalAlignment="Center" Margin="15">
                                    <TextBlock Text="يعتمد" FontWeight="Bold" FontSize="14" HorizontalAlignment="Center" Foreground="Black" Margin="0,0,0,35"/>
                                    <TextBlock Text="م/محمد محمد الديلمي" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="Black"/>
                                    <TextBlock Text="مدير الفرع" HorizontalAlignment="Center" FontSize="11" FontStyle="Italic" Foreground="Black" Margin="0,4,0,0"/>
                                </StackPanel>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- رقم الصفحة الأولى -->
                    <Border Background="Transparent" Padding="0,20,0,10" HorizontalAlignment="Center">
                        <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                            <TextBlock Text="1" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                        </Border>
                    </Border>

                </StackPanel>
                </Border>

        <!-- فاصل بين الصفحات -->
        <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

        <!-- الصفحة الثانية: عقد إيجار السيارة (الجزء الأول) -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                <StackPanel Margin="5">
                    <!-- Compact Contract Header -->
                    <Grid Margin="0,0,0,15">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Top Decorative Border -->
                        <Border Grid.Row="0" Height="2" Background="#8B4513" Margin="0,0,0,10"/>

                        <!-- Header Content -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Left: Contract Numbers -->
                            <Border Grid.Column="0" Background="#FFF8DC" BorderBrush="#D4AF37" BorderThickness="1" Padding="6,4" VerticalAlignment="Top">
                                <StackPanel>
                                    <TextBlock Text="معلومات العقد" FontSize="8" FontWeight="Bold" Foreground="#8B4513" HorizontalAlignment="Center"/>
                                    <TextBlock Text="رقم العقد: 2025001" FontSize="8" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,2,0,1" Foreground="#333333"/>
                                    <TextBlock Text="رقم أرشيف: 911_130526" FontSize="8" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                                </StackPanel>
                            </Border>

                            <!-- Center: Compact Contract Title -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <Border Background="#8B4513" Padding="12,8" Margin="0,0,0,5">
                                    <TextBlock Text="عقد اتفاق" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="White"/>
                                </Border>
                                <Border BorderBrush="#8B4513" BorderThickness="2" Padding="10,6" Background="#FFF8DC">
                                    <TextBlock Text="إيجــــــــــــــــــار سيـــــــــــــــــــــــــارة" FontSize="12" FontWeight="Bold"
                                             HorizontalAlignment="Center" Foreground="#8B4513"/>
                                </Border>
                            </StackPanel>

                            <!-- Right: Compact SFD Logo -->
                            <Border Grid.Column="2" Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="1" Padding="6,4" HorizontalAlignment="Right" VerticalAlignment="Top">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="الصندوق" FontSize="8" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4"/>
                                    <TextBlock Text="الاجتماعي" FontSize="8" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4"/>
                                    <TextBlock Text="للتنمية" FontSize="8" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4"/>
                                    <TextBlock Text="SFD" FontSize="7" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4" Margin="0,2,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Grid>

                    <!-- Compact Contract Introduction -->
                    <Border Background="#F8F9FA" BorderBrush="#6C757D" BorderThickness="1" Padding="8,6" Margin="0,0,0,8">
                        <StackPanel>
                            <TextBlock Text="تاريخ إبرام العقد" FontWeight="Bold" FontSize="10" Foreground="#495057" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                            <TextBlock FontSize="10" TextWrapping="Wrap" LineHeight="16" TextAlignment="Center" Foreground="#333333">
                                <Run Text="أنه في يوم "/>
                                <Run Text="{Binding ReportData.StartDateArabic}" FontWeight="Bold" Foreground="#8B4513"/>
                                <Run Text=" الموافق "/>
                                <Run Text="{Binding ReportData.StartDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold" Foreground="#8B4513"/>
                                <Run Text="م بأمانة العاصمة تم بين كل من:"/>
                            </TextBlock>
                        </StackPanel>
                    </Border>

                    <!-- Compact Parties Section -->
                    <StackPanel Margin="0,0,0,8">
                        <!-- First Party -->
                        <Border Background="#E8F5E8" BorderBrush="#28A745" BorderThickness="1" Padding="8,6" Margin="0,0,0,6">
                            <StackPanel>
                                <TextBlock Text="الطرف الأول (مالك السيارة)" FontWeight="Bold" FontSize="10" Foreground="#155724" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock FontSize="9" TextWrapping="Wrap" LineHeight="14" TextAlignment="Justify" Foreground="#155724">
                                    <Run Text="الأخ / "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.DriverName}" FontWeight="Bold" Foreground="#28A745"/>
                                    <Run Text=" يحمل بطاقة شخصية رقم ("/>
                                    <Run Text="{Binding ReportData.WinnerDriver.NationalId}" FontWeight="Bold" Foreground="#28A745"/>
                                    <Run Text=")، صادرة من "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.IssuePlace}" FontWeight="Bold" Foreground="#28A745"/>
                                    <Run Text=" بتاريخ "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.IssueDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold" Foreground="#28A745"/>
                                    <Run Text="، وبهذا العقد هو مالك السيارة وسائقها الطرف الأول."/>
                                </TextBlock>
                            </StackPanel>
                        </Border>

                        <!-- Second Party -->
                        <Border Background="#E7F3FF" BorderBrush="#007BFF" BorderThickness="1" Padding="8,6">
                            <StackPanel>
                                <TextBlock Text="الطرف الثاني (الصندوق الاجتماعي للتنمية)" FontWeight="Bold" FontSize="10" Foreground="#004085" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                <TextBlock FontSize="9" TextWrapping="Wrap" LineHeight="14" TextAlignment="Justify" Foreground="#004085">
                                    <Run Text="الصندوق الاجتماعي للتنمية- المركز الرئيسي ومقره شارع الشهيد إبراهيم الحمدي (شارع الستين الجنوبي) مقابل الجهاز المركزي للرقابة والمحاسبة، ويمثله في هذا العقد الأستاذ "/>
                                    <Run Text="عبدالله علي الديلمي" FontWeight="Bold" Foreground="#007BFF"/>
                                    <Run Text=" بصفته المدير التنفيذي للصندوق ويسمى في هذا العقد الطرف الثاني."/>
                                </TextBlock>
                            </StackPanel>
                        </Border>
                    </StackPanel>

                    <!-- Enhanced Agreement Title -->
                    <Border Background="#8B4513" CornerRadius="15" Padding="20,12" Margin="0,0,0,20">
                        <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                            <TextBlock Text="🤝" FontSize="18" Margin="0,0,10,0" VerticalAlignment="Center"/>
                            <TextBlock Text="الاتفاق والتراضي على ما يلي:" FontSize="15" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                            <TextBlock Text="🤝" FontSize="18" Margin="10,0,0,0" VerticalAlignment="Center"/>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced First: Vehicle Specifications -->
                    <Border Background="#FFF8DC" BorderBrush="#DAA520" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="🚗" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="أولاً: مواصفات السيارة" FontSize="14" FontWeight="Bold" Foreground="#B8860B" VerticalAlignment="Center"/>
                            </StackPanel>
                            <Border Background="White" BorderBrush="#DAA520" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="#333333">
                                    <Run Text="أجر الطرف الأول للطرف الثاني سيارة "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.VehicleType}" FontWeight="Bold" Foreground="#DAA520"/>
                                    <Run Text=" موديل "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.ManufactureYear}" FontWeight="Bold" Foreground="#DAA520"/>
                                    <Run Text=" لون "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.VehicleColor}" FontWeight="Bold" Foreground="#DAA520"/>
                                    <Run Text=" برقم ("/>
                                    <Run Text="{Binding ReportData.WinnerDriver.VehicleNumber}" FontWeight="Bold" Foreground="#DAA520"/>
                                    <Run Text=") المملوك له بموجب رخصة تسيير مركبة خصوصي رقم "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.LicenseNumber}" FontWeight="Bold" Foreground="#DAA520"/>
                                    <Run Text=" الصادرة بتاريخ "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.LicenseIssueDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold" Foreground="#DAA520"/>
                                    <Run Text=". ويسمى فيما بعد وسيلة النقل بموجب أحكام وشروط هذا العقد."/>
                                </TextBlock>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Second: Purpose -->
                    <Border Background="#E8F5E8" BorderBrush="#28A745" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="🎯" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="ثانياً: غرض الانتفاع" FontSize="14" FontWeight="Bold" Foreground="#155724" VerticalAlignment="Center"/>
                            </StackPanel>
                            <Border Background="White" BorderBrush="#28A745" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="#333333">
                                    <Run Text="اتفق الطرفان على استئجار الطرف الثاني وسيلة نقل &quot;سيارة مع سائقها&quot; لمرافقة الأخ/"/>
                                    <Run Text="{Binding ReportData.VisitConductor}" FontWeight="Bold" Foreground="#28A745"/>
                                    <Run Text="، صفته "/>
                                    <Run Text="{Binding ReportData.VisitConductorRank}" FontWeight="Bold" Foreground="#28A745"/>
                                    <Run Text=" أثناء تنفيذ المهمة المطلوبة منه."/>
                                </TextBlock>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Third: Rental Period -->
                    <Border Background="#E7F3FF" BorderBrush="#007BFF" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="📅" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="ثالثاً: المدة الإيجارية" FontSize="14" FontWeight="Bold" Foreground="#004085" VerticalAlignment="Center"/>
                            </StackPanel>
                            <Border Background="White" BorderBrush="#007BFF" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="#333333">
                                    <Run Text="اتفق الطرفان على أن المدة الإيجارية لتنفيذ المهمة تبدأ من يوم "/>
                                    <Run Text="{Binding ReportData.StartDateArabic}" FontWeight="Bold" Foreground="#007BFF"/>
                                    <Run Text=" الموافق "/>
                                    <Run Text="{Binding ReportData.StartDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold" Foreground="#007BFF"/>
                                    <Run Text=" ولمدة "/>
                                    <Run Text="{Binding ReportData.DaysCount}" FontWeight="Bold" Foreground="#007BFF"/>
                                    <Run Text=" أيام من تاريخ أول يوم سفر، وتنتهي يوم "/>
                                    <Run Text="{Binding ReportData.EndDateArabic}" FontWeight="Bold" Foreground="#007BFF"/>
                                    <Run Text=" الموافق "/>
                                    <Run Text="{Binding ReportData.EndDate, StringFormat=dd/MM/yyyy}" FontWeight="Bold" Foreground="#007BFF"/>
                                    <Run Text="، وتسير بموجب وثيقة خطة تنفيذ المهمة المرفقة بالعقد."/>
                                </TextBlock>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Fourth: Rental Value -->
                    <Border Background="#FFF3CD" BorderBrush="#FFC107" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="💰" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="رابعاً: القيمة الايجارية (الإجارة)" FontSize="14" FontWeight="Bold" Foreground="#856404" VerticalAlignment="Center"/>
                            </StackPanel>
                            <Border Background="White" BorderBrush="#FFC107" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="#333333">
                                    <Run Text="اتفق الطرفان على أن القيمة الإيجارية لوسيلة النقل خلال فترة تنفيذ المهمة بإكملها مبلغ وقدره "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.TotalPrice}" FontWeight="Bold" Foreground="#FFC107"/>
                                    <Run Text=" ريال يمني وتدفع من الطرف الثاني للطرف الأول وفقاً للإجراءات المالية للصندوق وتصرف بحسب عدد الأيام الفعلية المنجزة."/>
                                </TextBlock>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Fifth: Ownership Declaration -->
                    <Border Background="#F8D7DA" BorderBrush="#DC3545" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="📋" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="خامساً: إقرار الملكية" FontSize="14" FontWeight="Bold" Foreground="#721C24" VerticalAlignment="Center"/>
                            </StackPanel>
                            <Border Background="White" BorderBrush="#DC3545" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="#333333">
                                    <Run Text="يقر الطرف الأول بموجب هذا العقد بأنه "/>
                                    <Run Text="المالك الشرعي" FontWeight="Bold" Foreground="#DC3545"/>
                                    <Run Text=" لوسيلة النقل، وأنه حائز على وثائق إثبات الملكية الشرعية &quot;سارية المفعول&quot; وأنها خالية من أي التزامات شخصية أو عينية أو مطالبات أو حقوق للغير."/>
                                </TextBlock>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Sixth: First Party Obligations -->
                    <Border Background="#D1ECF1" BorderBrush="#17A2B8" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="📝" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="سادساً: التزامات الطرف الأول" FontSize="14" FontWeight="Bold" Foreground="#0C5460" VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- Obligation Items -->
                            <StackPanel>
                                <!-- First Obligation -->
                                <Border Background="White" BorderBrush="#17A2B8" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="✅" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="ينفذ العقد بنفسه وأن يبذل في تأديته من العناية مايبذله الشخص المعتاد أو من يمثله بتفويض رسمي."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <!-- Second Obligation -->
                                <Border Background="White" BorderBrush="#17A2B8" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="✅" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="الامتثال بأخلاقيات ومدونة سلوك الصندوق الاجتماعي للتنمية أثناء الرحلة."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <!-- Third Obligation -->
                                <Border Background="White" BorderBrush="#17A2B8" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="⚠️" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="الحرص على سلامة الركاب والالتزام بكافة قواعد وآداب المرور وقانونه منها:"/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <!-- Safety Rules Sub-section -->
                                <Border Background="#FFF3CD" BorderBrush="#FFC107" BorderThickness="1" CornerRadius="8" Padding="15,10" Margin="15,5,0,8">
                                    <StackPanel>
                                        <TextBlock Text="🛡️ قواعد السلامة والمرور:" FontSize="11" FontWeight="Bold" Foreground="#856404" Margin="0,0,0,8"/>

                                        <!-- Safety Rule 1 -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                            <TextBlock Text="🔒" FontSize="10" Margin="0,0,5,0" VerticalAlignment="Top"/>
                                            <TextBlock FontSize="10" TextWrapping="Wrap" LineHeight="16" TextAlignment="Justify" Foreground="#333333">
                                                <Run Text="يجب على السائق وكافة الركاب ربط حزام الأمان في كل الأوقات أثناء حركة المركبة ولا يسمح للراكب الجلوس بدونه."/>
                                            </TextBlock>
                                        </StackPanel>

                                        <!-- Safety Rule 2 -->
                                        <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                            <TextBlock Text="📵" FontSize="10" Margin="0,0,5,0" VerticalAlignment="Top"/>
                                            <TextBlock FontSize="10" TextWrapping="Wrap" LineHeight="16" TextAlignment="Justify" Foreground="#333333">
                                                <Run Text="يمنع على السائقين استخدام الهاتف المحمول أثناء القيادة."/>
                                            </TextBlock>
                                        </StackPanel>

                                        <!-- Speed Limits -->
                                        <StackPanel Margin="0,5,0,0">
                                            <StackPanel Orientation="Horizontal" Margin="0,0,0,5">
                                                <TextBlock Text="🚦" FontSize="10" Margin="0,0,5,0" VerticalAlignment="Top"/>
                                                <TextBlock FontSize="10" TextWrapping="Wrap" LineHeight="16" TextAlignment="Justify" Foreground="#333333">
                                                    <Run Text="الالتزام بحدود السرعة في الطرق العامة، ولكن لا يسمح بتجاوز السرعة تحت أي ظرف من الظروف كما يلي:"/>
                                                </TextBlock>
                                            </StackPanel>

                                            <!-- Speed Limit Details -->
                                            <Border Background="White" BorderBrush="#FFC107" BorderThickness="1" CornerRadius="5" Padding="10,8" Margin="15,5,0,0">
                                                <StackPanel>
                                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,3">
                                                        <TextBlock Text="🛣️" FontSize="9" Margin="0,0,5,0"/>
                                                        <TextBlock Text="100 كم/الساعة على الطرق العامة المسفلتة كحد أقصى" FontSize="9" Foreground="#333333"/>
                                                    </StackPanel>
                                                    <StackPanel Orientation="Horizontal" Margin="0,0,0,3">
                                                        <TextBlock Text="🏔️" FontSize="9" Margin="0,0,5,0"/>
                                                        <TextBlock Text="60 كم/الساعة على الطرق الغير مسفلتة والترابية كحد أقصى" FontSize="9" Foreground="#333333"/>
                                                    </StackPanel>
                                                    <StackPanel Orientation="Horizontal">
                                                        <TextBlock Text="🏘️" FontSize="9" Margin="0,0,5,0"/>
                                                        <TextBlock Text="40 كم/الساعة على الطرق داخل القرى وفي الأحياء السكنية كحد أقصى" FontSize="9" Foreground="#333333"/>
                                                    </StackPanel>
                                                </StackPanel>
                                            </Border>
                                        </StackPanel>
                                    </StackPanel>
                                </Border>

                                <!-- Additional Obligations -->
                                <Border Background="White" BorderBrush="#17A2B8" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🚫" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="عدم نقل أي ركاب آخرين عدا الطرف الثاني أثناء أداء المهام الميدانية."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <Border Background="White" BorderBrush="#17A2B8" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📄" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="اصطحاب كافة وثائق الملكية أثناء تنفيذ المهمة."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <Border Background="White" BorderBrush="#17A2B8" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🔧" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="الصيانة الدورية لوسيلة النقل وفحصها قبل تنفيذ المهمة."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <Border Background="White" BorderBrush="#17A2B8" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📞" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="سرعة الإبلاغ عن أي حوادث تحدث أثناء قيادة المركبة خلال ساعات العمل أو خارجها."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <Border Background="White" BorderBrush="#17A2B8" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,0">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🧰" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="اصطحاب أدوات السلامة مثل طفاية الحريق (نوع بودر حجم 1كيلو) – صندوق إسعافات أولية – مثلث التحذير – عدة صيانة السيارة مثل &quot;إطار احتياطي..... وغيرها&quot;"/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- رقم الصفحة الثانية -->
                    <Border Background="Transparent" Padding="0,20,0,10" HorizontalAlignment="Center">
                        <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                            <TextBlock Text="2" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                        </Border>
                    </Border>

                </StackPanel>
                </Border>

        <!-- فاصل بين الصفحات -->
        <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

        <!-- الصفحة الثالثة: عقد إيجار السيارة (الجزء الثاني) -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                <StackPanel Margin="5">
                    <!-- Enhanced Page 2 Header -->
                    <Grid Margin="0,0,0,30">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Top Decorative Border -->
                        <Border Grid.Row="0" Height="5" Background="#8B4513" CornerRadius="3" Margin="0,0,0,20"/>

                        <!-- Header Content -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Left: Contract Numbers -->
                            <Border Grid.Column="0" Background="#FFF8DC" BorderBrush="#D4AF37" BorderThickness="2" CornerRadius="10" Padding="12,8" VerticalAlignment="Top">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,5">
                                        <TextBlock Text="📄" FontSize="12" Margin="0,0,5,0"/>
                                        <TextBlock Text="معلومات العقد" FontSize="10" FontWeight="Bold" Foreground="#8B4513"/>
                                    </StackPanel>
                                    <TextBlock Text="رقم العقد: 2025001" FontSize="11" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,3" Foreground="#333333"/>
                                    <TextBlock Text="رقم أرشيف: 911_130526" FontSize="11" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                                </StackPanel>
                            </Border>

                            <!-- Center: Enhanced Contract Title -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <Border Background="#8B4513" CornerRadius="20" Padding="25,15" Margin="0,0,0,10">
                                    <StackPanel>
                                        <TextBlock Text="📜" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <TextBlock Text="تابع عقد اتفاق" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,5"/>
                                        <Border Height="2" Background="White" CornerRadius="1" Margin="20,0,20,0"/>
                                    </StackPanel>
                                </Border>
                                <Border BorderBrush="#8B4513" BorderThickness="3" CornerRadius="15" Padding="20,12" Background="#FFF8DC">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <TextBlock Text="🚗" FontSize="18" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="إيجــــــــــــــــــار سيـــــــــــــــــــــــــارة" FontSize="16" FontWeight="Bold"
                                                 HorizontalAlignment="Center" Foreground="#8B4513" VerticalAlignment="Center"/>
                                        <TextBlock Text="🚗" FontSize="18" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>

                            <!-- Right: Enhanced SFD Logo -->
                            <Border Grid.Column="2" Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="10" Padding="12,8" HorizontalAlignment="Right" VerticalAlignment="Top">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🏛️" FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                    <TextBlock Text="الصندوق" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4"/>
                                    <TextBlock Text="الاجتماعي" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4"/>
                                    <TextBlock Text="للتنمية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4"/>
                                    <TextBlock Text="SFD" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4" Margin="0,3,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Grid>

                    <!-- Enhanced Seventh: Second Party Obligations -->
                    <Border Background="#E7F3FF" BorderBrush="#007BFF" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="📋" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="سابعاً: التزامات الطرف الثاني" FontSize="14" FontWeight="Bold" Foreground="#004085" VerticalAlignment="Center"/>
                            </StackPanel>

                            <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="20" TextAlignment="Justify" Foreground="#004085" Margin="0,0,0,15">
                                <Run Text="يقر الطرف الثاني بأنه عاين كافة الوثائق المطلوبة في وسيلة النقل ووجد أنها مستوفية لكافة لوازمها ولذلك يلتزم بموجب هذا العقد بما يلي:"/>
                            </TextBlock>

                            <!-- Obligation Items -->
                            <StackPanel>
                                <!-- First Obligation -->
                                <Border Background="White" BorderBrush="#007BFF" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="أ" FontSize="12" FontWeight="Bold" Margin="0,0,8,0" VerticalAlignment="Top" Foreground="#007BFF"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="التزام ممثليه بتعليمات سائق وسيلة النقل."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <!-- Second Obligation -->
                                <Border Background="White" BorderBrush="#007BFF" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="ب" FontSize="12" FontWeight="Bold" Margin="0,0,8,0" VerticalAlignment="Top" Foreground="#007BFF"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="التزام ممثليه بوقت وموعد الرحلة."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <!-- Third Obligation -->
                                <Border Background="White" BorderBrush="#007BFF" BorderThickness="1" CornerRadius="8" Padding="12,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="ت" FontSize="12" FontWeight="Bold" Margin="0,0,8,0" VerticalAlignment="Top" Foreground="#007BFF"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="تبليغ السائق على الفور عند إلغاء أو قطع أو تمديد مهمة العمل بدون إنذار مسبق."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Eighth: Dispute Resolution -->
                    <Border Background="#FFF3CD" BorderBrush="#FFC107" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,12">
                                <TextBlock Text="⚖️" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="ثامناً: تسوية وحل المنازعات" FontSize="14" FontWeight="Bold" Foreground="#856404" VerticalAlignment="Center"/>
                            </StackPanel>
                            <Border Background="White" BorderBrush="#FFC107" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="#333333">
                                    <Run Text="كل نزاع ينشأ بين الطرفين أو خلفهما يتعلق بانعقاد أو تنفيذ أو تفسير هذا العقد، أو ما يتفرع عنه أو يرتبط به بأي وجه من الوجوه يتم حله وتسويته بينهما أولاً بالطرق الودية خلال "/>
                                    <Run Text="تسعين يوماً" FontWeight="Bold" Foreground="#FFC107"/>
                                    <Run Text="."/>
                                </TextBlock>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Ninth: Other Provisions -->
                    <Border Background="#F8D7DA" BorderBrush="#DC3545" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" Margin="0,0,0,15">
                                <TextBlock Text="📜" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="تاسعاً: أحكام أخرى" FontSize="14" FontWeight="Bold" Foreground="#721C24" VerticalAlignment="Center"/>
                            </StackPanel>

                            <!-- Provision Items -->
                            <StackPanel>
                                <!-- First Provision -->
                                <Border Background="White" BorderBrush="#DC3545" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="⏰" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="ينتهي العقد بانتهاء مدته المتفق عليها في العقد دون الحاجة إلى تنبيه أو إنذار ما لم يبلغ الطرف الثاني الطرف الأول برغبته في تمديد المدة."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <!-- Second Provision -->
                                <Border Background="White" BorderBrush="#DC3545" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,0,0,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="🛡️" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="الطرف الأول (مالك السيارة) المسؤول وحده على التأمين على نفسه وعلى سيارته من كافة الحوادث والاعتداءات من الغير، ولا يتحمل الصندوق أي مسؤولية تجاه ما قد يتعرض له أثناء تنفيذ المهمة."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>

                                <!-- Third Provision -->
                                <Border Background="White" BorderBrush="#DC3545" BorderThickness="1" CornerRadius="8" Padding="12,8">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="⚠️" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Top"/>
                                        <TextBlock FontSize="11" TextWrapping="Wrap" LineHeight="18" TextAlignment="Justify" Foreground="#333333">
                                            <Run Text="يتحمل الطرف الأول (مالك السيارة والسائق) وحده مسئولية تعويض الراكب عن أي ضرر قد يحدث له نتيجة إصابته بسبب وقوع حادث للسيارة."/>
                                        </TextBlock>
                                    </StackPanel>
                                </Border>
                            </StackPanel>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Contract Conclusion -->
                    <Border Background="#E8F5E8" BorderBrush="#28A745" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,20">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                                <TextBlock Text="📋" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="خاتمة العقد" FontWeight="Bold" FontSize="14" Foreground="#155724" VerticalAlignment="Center"/>
                                <TextBlock Text="📋" FontSize="16" Margin="8,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                            <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Center" Foreground="#155724">
                                <Run Text="حرر هذا العقد من "/>
                                <Run Text="ثلاث نسخ" FontWeight="Bold" Foreground="#28A745"/>
                                <Run Text=" بيد كل طرف نسخة منه، والنسخة الثالثة للتوثيق."/>
                            </TextBlock>
                        </StackPanel>
                    </Border>

                    <Border Background="#8B4513" CornerRadius="15" Padding="20,12" Margin="0,0,0,25">
                        <TextBlock FontSize="14" FontWeight="Bold" TextAlignment="Center" Foreground="White">
                            <Run Text="🤲 ولله الأمر كله وهو على كل شيء شهيد 🤲"/>
                        </TextBlock>
                    </Border>

                    <!-- Enhanced Contract Signatures -->
                    <Border Background="#F8F9FA" BorderBrush="#6C757D" BorderThickness="2" CornerRadius="15" Padding="25,20" Margin="0,0,0,20">
                        <StackPanel>
                            <!-- Signatures Header -->
                            <Border Background="#6C757D" CornerRadius="10" Padding="15,10" Margin="0,0,0,20">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <TextBlock Text="✍️" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="توقيعات أطراف العقد" FontWeight="Bold" FontSize="14"
                                             Foreground="White" VerticalAlignment="Center"/>
                                    <TextBlock Text="✍️" FontSize="16" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- First Party Signature -->
                                <Border Grid.Column="0" Background="White" BorderBrush="#28A745" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="👤" FontSize="18" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <TextBlock Text="الطرف الأول" FontWeight="Bold" FontSize="13" HorizontalAlignment="Center" Foreground="#155724" Margin="0,0,0,3"/>
                                        <TextBlock Text="(مالك السيارة)" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,0,0,15"/>
                                        <Border BorderBrush="#28A745" BorderThickness="0,0,0,2" Margin="0,0,0,10" Width="150"/>
                                        <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="#333333" Margin="0,0,0,8"/>
                                        <Border Background="#E8F5E8" BorderBrush="#28A745" BorderThickness="1" CornerRadius="5" Padding="8,4">
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.PhoneNumber}" HorizontalAlignment="Center" FontSize="10" Foreground="#155724"/>
                                        </Border>
                                    </StackPanel>
                                </Border>

                                <!-- Second Party Signature -->
                                <Border Grid.Column="1" Background="White" BorderBrush="#007BFF" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="🏛️" FontSize="18" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <TextBlock Text="الطرف الثاني" FontWeight="Bold" FontSize="13" HorizontalAlignment="Center" Foreground="#004085" Margin="0,0,0,3"/>
                                        <TextBlock Text="القائم بالمهمة" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#6C757D" Margin="0,0,0,15"/>
                                        <Border BorderBrush="#007BFF" BorderThickness="0,0,0,2" Margin="0,0,0,10" Width="150"/>
                                        <TextBlock Text="{Binding ReportData.VisitConductor}" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="#333333" Margin="0,0,0,8"/>
                                        <Border Background="#E7F3FF" BorderBrush="#007BFF" BorderThickness="1" CornerRadius="5" Padding="8,4">
                                            <TextBlock Text="عبدالله علي ناصر الأصرعي" HorizontalAlignment="Center" FontSize="10" FontWeight="Bold" Foreground="#004085"/>
                                        </Border>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Additional Signatures -->
                    <Border Background="#FFF8DC" BorderBrush="#D4AF37" BorderThickness="2" CornerRadius="15" Padding="25,20" Margin="0,10,0,0">
                        <StackPanel>
                            <!-- Additional Signatures Header -->
                            <Border Background="#D4AF37" CornerRadius="10" Padding="15,10" Margin="0,0,0,20">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <TextBlock Text="🏅" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="الاعتمادات الإدارية" FontWeight="Bold" FontSize="14"
                                             Foreground="White" VerticalAlignment="Center"/>
                                    <TextBlock Text="🏅" FontSize="16" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <Grid>
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- Branch Manager -->
                                <Border Grid.Column="0" Background="White" BorderBrush="#D4AF37" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="✅" FontSize="18" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <TextBlock Text="يعتمد الطرف الثاني" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="#B8860B" Margin="0,0,0,15"/>
                                        <Border BorderBrush="#D4AF37" BorderThickness="0,0,0,2" Margin="0,0,0,10" Width="150"/>
                                        <TextBlock Text="م/محمد محمد الديلمي" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="#333333" Margin="0,0,0,5"/>
                                        <Border Background="#FFF8DC" BorderBrush="#D4AF37" BorderThickness="1" CornerRadius="5" Padding="8,4">
                                            <TextBlock Text="مدير الفرع" HorizontalAlignment="Center" FontSize="10" FontStyle="Italic" Foreground="#B8860B"/>
                                        </Border>
                                    </StackPanel>
                                </Border>

                                <!-- Service Provider -->
                                <Border Grid.Column="1" Background="White" BorderBrush="#6C757D" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="🔧" FontSize="18" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <TextBlock Text="مزود الخدمة" FontWeight="Bold" FontSize="12" HorizontalAlignment="Center" Foreground="#495057" Margin="0,0,0,15"/>
                                        <Border BorderBrush="#6C757D" BorderThickness="0,0,0,2" Margin="0,0,0,10" Width="150"/>
                                        <TextBlock Text="علي أحمد محمد المعطم" HorizontalAlignment="Center" FontSize="12" FontWeight="Bold" Foreground="#333333"/>
                                    </StackPanel>
                                </Border>
                            </Grid>
                        </StackPanel>
                    </Border>

                    <!-- رقم الصفحة الثالثة -->
                    <Border Background="Transparent" Padding="0,20,0,10" HorizontalAlignment="Center">
                        <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                            <TextBlock Text="3" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                        </Border>
                    </Border>

                </StackPanel>
                </Border>

        <!-- فاصل بين الصفحات -->
        <Border Height="30" Background="Transparent" Margin="0,10,0,10"/>

        <!-- الصفحة الرابعة: بيان وإقرار -->
                <Border Style="{StaticResource PrintPageStyle}" Margin="5,10,5,10">
                <StackPanel Margin="5">
                    <!-- Enhanced Declaration Header -->
                    <Grid Margin="0,0,0,30">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                        </Grid.RowDefinitions>

                        <!-- Top Decorative Border -->
                        <Border Grid.Row="0" Height="5" Background="#6F42C1" CornerRadius="3" Margin="0,0,0,20"/>

                        <!-- Header Content -->
                        <Grid Grid.Row="1">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*"/>
                                <ColumnDefinition Width="2*"/>
                                <ColumnDefinition Width="*"/>
                            </Grid.ColumnDefinitions>

                            <!-- Left: Contract Numbers -->
                            <Border Grid.Column="0" Background="#F8F9FA" BorderBrush="#6C757D" BorderThickness="2" CornerRadius="10" Padding="12,8" VerticalAlignment="Top">
                                <StackPanel>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,5">
                                        <TextBlock Text="📄" FontSize="12" Margin="0,0,5,0"/>
                                        <TextBlock Text="معلومات العقد" FontSize="10" FontWeight="Bold" Foreground="#495057"/>
                                    </StackPanel>
                                    <TextBlock Text="رقم العقد: 2025001" FontSize="11" FontWeight="Bold" HorizontalAlignment="Center" Margin="0,0,0,3" Foreground="#333333"/>
                                    <TextBlock Text="رقم أرشيف: 911_130526" FontSize="11" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#333333"/>
                                </StackPanel>
                            </Border>

                            <!-- Center: Enhanced Declaration Title -->
                            <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                                <Border Background="#6F42C1" CornerRadius="20" Padding="25,15" Margin="0,0,0,10">
                                    <StackPanel>
                                        <TextBlock Text="📋" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,8"/>
                                        <TextBlock Text="بيان وإقرار" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center" Foreground="White" Margin="0,0,0,5"/>
                                        <Border Height="2" Background="White" CornerRadius="1" Margin="20,0,20,0"/>
                                    </StackPanel>
                                </Border>
                                <Border BorderBrush="#6F42C1" BorderThickness="3" CornerRadius="15" Padding="20,12" Background="#F8F9FA">
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <TextBlock Text="📝" FontSize="18" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                        <TextBlock Text="تضارب المصالح والعلاقات العائلية" FontSize="14" FontWeight="Bold"
                                                 HorizontalAlignment="Center" Foreground="#6F42C1" VerticalAlignment="Center"/>
                                        <TextBlock Text="📝" FontSize="18" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </StackPanel>

                            <!-- Right: Enhanced SFD Logo -->
                            <Border Grid.Column="2" Background="#F0F8FF" BorderBrush="#4682B4" BorderThickness="2" CornerRadius="10" Padding="12,8" HorizontalAlignment="Right" VerticalAlignment="Top">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🏛️" FontSize="16" HorizontalAlignment="Center" Margin="0,0,0,5"/>
                                    <TextBlock Text="الصندوق" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4"/>
                                    <TextBlock Text="الاجتماعي" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4"/>
                                    <TextBlock Text="للتنمية" FontSize="12" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4"/>
                                    <TextBlock Text="SFD" FontSize="10" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4682B4" Margin="0,3,0,0"/>
                                </StackPanel>
                            </Border>
                        </Grid>
                    </Grid>

                    <!-- Enhanced Declaration Introduction -->
                    <Border Background="#E7F3FF" BorderBrush="#007BFF" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,0,0,20">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,12">
                                <TextBlock Text="📢" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="إقرار مزود الخدمة" FontWeight="Bold" FontSize="14" Foreground="#004085" VerticalAlignment="Center"/>
                                <TextBlock Text="📢" FontSize="16" Margin="8,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                            <Border Background="White" BorderBrush="#007BFF" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="#333333">
                                    <Run Text="إقراراً مزود الخدمة صاحب سيارة رقم "/>
                                    <Run Text="{Binding ReportData.WinnerDriver.VehicleNumber}" FontWeight="Bold" Foreground="#007BFF"/>
                                    <Run Text=" بأنني تسلمت من الصندوق الاجتماعي للتنمية نسخة من "/>
                                    <Run Text="مدونة تضارب المصالح" FontWeight="Bold" Foreground="#007BFF"/>
                                    <Run Text="، وخصوص عقدي بلدى العاملين في الصندوق فإنني أقر بما يلي:"/>
                                </TextBlock>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced First Declaration Section -->
                    <Border BorderBrush="#28A745" BorderThickness="2" CornerRadius="12" Margin="0,0,0,20" Background="White">
                        <StackPanel>
                            <!-- Section Header -->
                            <Border Background="#28A745" CornerRadius="10,10,0,0" Padding="15,12">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <TextBlock Text="👥" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="القسم الأول: العلاقات العائلية" FontWeight="Bold" FontSize="14"
                                             Foreground="White" VerticalAlignment="Center"/>
                                    <TextBlock Text="👥" FontSize="16" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- Question Section -->
                            <Border Background="#E8F5E8" Padding="20,15">
                                <StackPanel>
                                    <Border Background="White" BorderBrush="#28A745" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" FontSize="12" VerticalAlignment="Center" Foreground="#333333">
                                                <Run Text="1) أنه "/>
                                                <Run Text="ليس لديه علاقة عائلية" FontWeight="Bold" Foreground="#28A745"/>
                                                <Run Text=" مع أحد العاملين"/>
                                            </TextBlock>
                                            <Border Grid.Column="1" Background="#28A745" CornerRadius="5" Padding="8,4" Margin="15,0,5,0">
                                                <CheckBox VerticalAlignment="Center" Foreground="White"/>
                                            </Border>
                                            <TextBlock Grid.Column="2" Text="نعم" FontSize="11" FontWeight="Bold" VerticalAlignment="Center" Foreground="#28A745"/>
                                        </Grid>
                                    </Border>

                                    <Border Background="#FFF3CD" BorderBrush="#FFC107" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,10,0,0">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="⚠️" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" FontSize="11" FontWeight="Bold" Foreground="#856404" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- Enhanced Employee Details Table -->
                            <Border BorderBrush="#28A745" BorderThickness="2" CornerRadius="8" Margin="0,10,0,0" Background="White">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Enhanced Table Headers -->
                                    <Border Grid.Row="0" Grid.Column="0" BorderBrush="#28A745" BorderThickness="0,0,1,1" Background="#E8F5E8" Padding="10,8">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="👤" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                            <TextBlock Text="اسم العامل" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#155724"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Grid.Row="0" Grid.Column="1" BorderBrush="#28A745" BorderThickness="0,0,1,1" Background="#E8F5E8" Padding="10,8">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="🏢" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                            <TextBlock Text="فرع الصندوق" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#155724"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Grid.Row="0" Grid.Column="2" BorderBrush="#28A745" BorderThickness="0,0,0,1" Background="#E8F5E8" Padding="10,8">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="👨‍👩‍👧‍👦" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                            <TextBlock Text="نوع العلاقة العائلية" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#155724"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Enhanced Empty Rows for Data Entry -->
                                    <Border Grid.Row="1" Grid.Column="0" BorderBrush="#28A745" BorderThickness="0,0,1,1" Background="#F8F9FA" Padding="8" Height="35"/>
                                    <Border Grid.Row="1" Grid.Column="1" BorderBrush="#28A745" BorderThickness="0,0,1,1" Background="#F8F9FA" Padding="8" Height="35"/>
                                    <Border Grid.Row="1" Grid.Column="2" BorderBrush="#28A745" BorderThickness="0,0,0,1" Background="#F8F9FA" Padding="8" Height="35"/>

                                    <Border Grid.Row="2" Grid.Column="0" BorderBrush="#28A745" BorderThickness="0,0,1,1" Background="White" Padding="8" Height="35"/>
                                    <Border Grid.Row="2" Grid.Column="1" BorderBrush="#28A745" BorderThickness="0,0,1,1" Background="White" Padding="8" Height="35"/>
                                    <Border Grid.Row="2" Grid.Column="2" BorderBrush="#28A745" BorderThickness="0,0,0,1" Background="White" Padding="8" Height="35"/>

                                    <Border Grid.Row="3" Grid.Column="0" BorderBrush="#28A745" BorderThickness="0,0,1,0" Background="#F8F9FA" Padding="8" Height="35"/>
                                    <Border Grid.Row="3" Grid.Column="1" BorderBrush="#28A745" BorderThickness="0,0,1,0" Background="#F8F9FA" Padding="8" Height="35"/>
                                    <Border Grid.Row="3" Grid.Column="2" BorderBrush="#28A745" BorderThickness="0,0,0,0" Background="#F8F9FA" Padding="8" Height="35"/>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Second Declaration Section -->
                    <Border BorderBrush="#DC3545" BorderThickness="2" CornerRadius="12" Margin="0,0,0,20" Background="White">
                        <StackPanel>
                            <!-- Section Header -->
                            <Border Background="#DC3545" CornerRadius="10,10,0,0" Padding="15,12">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <TextBlock Text="💼" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="القسم الثاني: المصالح التجارية" FontWeight="Bold" FontSize="14"
                                             Foreground="White" VerticalAlignment="Center"/>
                                    <TextBlock Text="💼" FontSize="16" Margin="8,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- Question Section -->
                            <Border Background="#F8D7DA" Padding="20,15">
                                <StackPanel>
                                    <Border Background="White" BorderBrush="#DC3545" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="*"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                                <ColumnDefinition Width="Auto"/>
                                            </Grid.ColumnDefinitions>

                                            <TextBlock Grid.Column="0" FontSize="12" VerticalAlignment="Center" Foreground="#333333">
                                                <Run Text="2) أنه "/>
                                                <Run Text="مصالح مباشرة أو غير مباشرة" FontWeight="Bold" Foreground="#DC3545"/>
                                                <Run Text=" مع أحد العاملين"/>
                                            </TextBlock>

                                            <!-- Yes Option -->
                                            <Border Grid.Column="1" Background="#28A745" CornerRadius="5" Padding="8,4" Margin="15,0,5,0">
                                                <CheckBox VerticalAlignment="Center" Foreground="White"/>
                                            </Border>
                                            <TextBlock Grid.Column="2" Text="نعم" FontSize="11" FontWeight="Bold" VerticalAlignment="Center" Foreground="#28A745" Margin="0,0,15,0"/>

                                            <!-- No Option -->
                                            <Border Grid.Column="3" Background="#DC3545" CornerRadius="5" Padding="8,4" Margin="5,0">
                                                <CheckBox VerticalAlignment="Center" Foreground="White"/>
                                            </Border>
                                            <TextBlock Grid.Column="4" Text="لا" FontSize="11" FontWeight="Bold" VerticalAlignment="Center" Foreground="#DC3545"/>
                                        </Grid>
                                    </Border>

                                    <Border Background="#FFF3CD" BorderBrush="#FFC107" BorderThickness="1" CornerRadius="8" Padding="12,8" Margin="0,10,0,0">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="⚠️" FontSize="12" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                            <TextBlock Text="في حال كانت الإجابة نعم يتم تعبئة ما يلي:" FontSize="11" FontWeight="Bold" Foreground="#856404" VerticalAlignment="Center"/>
                                        </StackPanel>
                                    </Border>
                                </StackPanel>
                            </Border>

                            <!-- Enhanced Second Table -->
                            <Border BorderBrush="#DC3545" BorderThickness="2" CornerRadius="8" Margin="0,10,0,0" Background="White">
                                <Grid>
                                    <Grid.RowDefinitions>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                        <RowDefinition Height="Auto"/>
                                    </Grid.RowDefinitions>
                                    <Grid.ColumnDefinitions>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                        <ColumnDefinition Width="*"/>
                                    </Grid.ColumnDefinitions>

                                    <!-- Enhanced Table Headers -->
                                    <Border Grid.Row="0" Grid.Column="0" BorderBrush="#DC3545" BorderThickness="0,0,1,1" Background="#F8D7DA" Padding="10,8">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="👤" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                            <TextBlock Text="اسم العامل" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#721C24"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Grid.Row="0" Grid.Column="1" BorderBrush="#DC3545" BorderThickness="0,0,1,1" Background="#F8D7DA" Padding="10,8">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="🏢" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                            <TextBlock Text="فرع الصندوق" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#721C24"/>
                                        </StackPanel>
                                    </Border>
                                    <Border Grid.Row="0" Grid.Column="2" BorderBrush="#DC3545" BorderThickness="0,0,0,1" Background="#F8D7DA" Padding="10,8">
                                        <StackPanel HorizontalAlignment="Center">
                                            <TextBlock Text="💼" FontSize="12" HorizontalAlignment="Center" Margin="0,0,0,3"/>
                                            <TextBlock Text="نوع المصلحة" FontWeight="Bold" FontSize="11" HorizontalAlignment="Center" Foreground="#721C24"/>
                                        </StackPanel>
                                    </Border>

                                    <!-- Enhanced Empty Rows -->
                                    <Border Grid.Row="1" Grid.Column="0" BorderBrush="#DC3545" BorderThickness="0,0,1,1" Background="#FFF5F5" Padding="8" Height="35"/>
                                    <Border Grid.Row="1" Grid.Column="1" BorderBrush="#DC3545" BorderThickness="0,0,1,1" Background="#FFF5F5" Padding="8" Height="35"/>
                                    <Border Grid.Row="1" Grid.Column="2" BorderBrush="#DC3545" BorderThickness="0,0,0,1" Background="#FFF5F5" Padding="8" Height="35"/>

                                    <Border Grid.Row="2" Grid.Column="0" BorderBrush="#DC3545" BorderThickness="0,0,1,1" Background="White" Padding="8" Height="35"/>
                                    <Border Grid.Row="2" Grid.Column="1" BorderBrush="#DC3545" BorderThickness="0,0,1,1" Background="White" Padding="8" Height="35"/>
                                    <Border Grid.Row="2" Grid.Column="2" BorderBrush="#DC3545" BorderThickness="0,0,0,1" Background="White" Padding="8" Height="35"/>

                                    <Border Grid.Row="3" Grid.Column="0" BorderBrush="#DC3545" BorderThickness="0,0,1,0" Background="#FFF5F5" Padding="8" Height="35"/>
                                    <Border Grid.Row="3" Grid.Column="1" BorderBrush="#DC3545" BorderThickness="0,0,1,0" Background="#FFF5F5" Padding="8" Height="35"/>
                                    <Border Grid.Row="3" Grid.Column="2" BorderBrush="#DC3545" BorderThickness="0,0,0,0" Background="#FFF5F5" Padding="8" Height="35"/>
                                </Grid>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- Enhanced Final Declaration -->
                    <Border Background="#6F42C1" BorderThickness="2" CornerRadius="12" Padding="20,15" Margin="0,20,0,15">
                        <StackPanel>
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,12">
                                <TextBlock Text="📝" FontSize="16" Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <TextBlock Text="الإقرار النهائي" FontWeight="Bold" FontSize="14" Foreground="White" VerticalAlignment="Center"/>
                                <TextBlock Text="📝" FontSize="16" Margin="8,0,0,0" VerticalAlignment="Center"/>
                            </StackPanel>
                            <Border Background="White" BorderBrush="#6F42C1" BorderThickness="1" CornerRadius="8" Padding="15,12">
                                <TextBlock FontSize="12" TextWrapping="Wrap" LineHeight="22" TextAlignment="Justify" Foreground="#333333">
                                    <Run Text="أنا الموقع أدناه أقر بأن جميع البيانات المذكورة أعلاه "/>
                                    <Run Text="صحيحة" FontWeight="Bold" Foreground="#6F42C1"/>
                                    <Run Text="، وبحق للصندوق اتخاذ الإجراءات التي يراها مناسبة تجاهي في حال عدم صحة البيانات المذكورة."/>
                                </TextBlock>
                            </Border>
                        </StackPanel>
                    </Border>

                    <Border Background="#8B4513" CornerRadius="15" Padding="20,12" Margin="0,0,0,25">
                        <TextBlock FontSize="14" FontWeight="Bold" TextAlignment="Center" Foreground="White">
                            <Run Text="🤲 والله الموفق 🤲"/>
                        </TextBlock>
                    </Border>

                    <!-- Enhanced Service Provider Signature -->
                    <Border Background="#F8F9FA" BorderBrush="#6C757D" BorderThickness="3" CornerRadius="15" Padding="25,20" Margin="0,0,0,0">
                        <StackPanel>
                            <!-- Signature Header -->
                            <Border Background="#6C757D" CornerRadius="10" Padding="15,12" Margin="0,0,0,20">
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                    <TextBlock Text="✍️" FontSize="18" Margin="0,0,10,0" VerticalAlignment="Center"/>
                                    <TextBlock Text="توقيع مزود الخدمة" FontSize="16" FontWeight="Bold" Foreground="White" VerticalAlignment="Center"/>
                                    <TextBlock Text="✍️" FontSize="18" Margin="10,0,0,0" VerticalAlignment="Center"/>
                                </StackPanel>
                            </Border>

                            <!-- Signature Content -->
                            <Border Background="White" BorderBrush="#6C757D" BorderThickness="2" CornerRadius="12" Padding="25,20">
                                <StackPanel HorizontalAlignment="Center">
                                    <TextBlock Text="🚗" FontSize="24" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                                    <!-- Name Section -->
                                    <StackPanel Margin="0,0,0,20">
                                        <TextBlock Text="الاسم:" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#495057" Margin="0,0,0,8"/>
                                        <Border Background="#E9ECEF" BorderBrush="#6C757D" BorderThickness="1" CornerRadius="8" Padding="15,10">
                                            <TextBlock Text="{Binding ReportData.WinnerDriver.DriverName}" FontSize="14" FontWeight="Bold"
                                                     HorizontalAlignment="Center" Foreground="#333333"/>
                                        </Border>
                                    </StackPanel>

                                    <!-- Signature Section -->
                                    <StackPanel>
                                        <TextBlock Text="التوقيع:" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#495057" Margin="0,0,0,15"/>
                                        <Border BorderBrush="#6C757D" BorderThickness="0,0,0,3" Width="200" Height="50"/>
                                    </StackPanel>
                                </StackPanel>
                            </Border>
                        </StackPanel>
                    </Border>

                    <!-- رقم الصفحة الرابعة -->
                    <Border Background="Transparent" Padding="0,20,0,10" HorizontalAlignment="Center">
                        <Border Background="White" BorderBrush="#CCCCCC" BorderThickness="1" CornerRadius="5" Padding="12,6">
                            <TextBlock Text="4" FontSize="14" FontWeight="Bold" Foreground="#333333" HorizontalAlignment="Center"/>
                        </Border>
                    </Border>

                </StackPanel>
                </Border>

        </StackPanel>
    </ScrollViewer>
</UserControl>
