using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Timers;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة النسخ الاحتياطي التلقائي للنظام
    /// </summary>
    public class AutoBackupService : IDisposable
    {
        private readonly Timer _backupTimer;
        private readonly string _backupDirectory;
        private readonly string _databasePath;
        private bool _isDisposed = false;

        public AutoBackupService()
        {
            // إعداد مجلد النسخ الاحتياطي
            var appDataPath = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.ApplicationData), "SFDSystem");
            _backupDirectory = Path.Combine(appDataPath, "Backups");
            Directory.CreateDirectory(_backupDirectory);

            // مسار قاعدة البيانات
            _databasePath = Path.Combine(appDataPath, "SFDDatabase.db");

            // إعداد مؤقت النسخ الاحتياطي (كل 30 دقيقة)
            _backupTimer = new Timer(TimeSpan.FromMinutes(30).TotalMilliseconds);
            _backupTimer.Elapsed += OnBackupTimerElapsed;
            _backupTimer.AutoReset = true;
            _backupTimer.Enabled = true;

            System.Diagnostics.Debug.WriteLine("✅ تم تشغيل خدمة النسخ الاحتياطي التلقائي");
        }

        /// <summary>
        /// بدء خدمة النسخ الاحتياطي
        /// </summary>
        public void Start()
        {
            if (!_isDisposed)
            {
                _backupTimer.Start();
                System.Diagnostics.Debug.WriteLine("🔄 تم بدء خدمة النسخ الاحتياطي");
            }
        }

        /// <summary>
        /// إيقاف خدمة النسخ الاحتياطي
        /// </summary>
        public void Stop()
        {
            _backupTimer?.Stop();
            System.Diagnostics.Debug.WriteLine("⏹️ تم إيقاف خدمة النسخ الاحتياطي");
        }

        /// <summary>
        /// إنشاء نسخة احتياطية فورية
        /// </summary>
        public async Task<bool> CreateBackupNowAsync()
        {
            try
            {
                return await CreateBackupAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء النسخة الاحتياطية الفورية: {ex.Message}");
                return false;
            }
        }

        private async void OnBackupTimerElapsed(object? sender, ElapsedEventArgs e)
        {
            try
            {
                await CreateBackupAsync();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في النسخ الاحتياطي التلقائي: {ex.Message}");
            }
        }

        private async Task<bool> CreateBackupAsync()
        {
            try
            {
                if (!File.Exists(_databasePath))
                {
                    System.Diagnostics.Debug.WriteLine("⚠️ قاعدة البيانات غير موجودة للنسخ الاحتياطي");
                    return false;
                }

                // إنشاء اسم ملف النسخة الاحتياطية
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss");
                var backupFileName = $"SFDDatabase_Backup_{timestamp}.db";
                var backupFilePath = Path.Combine(_backupDirectory, backupFileName);

                // نسخ قاعدة البيانات
                await Task.Run(() => File.Copy(_databasePath, backupFilePath, true));

                // إنشاء ملف معلومات النسخة الاحتياطية
                var infoFileName = $"SFDDatabase_Backup_{timestamp}.info";
                var infoFilePath = Path.Combine(_backupDirectory, infoFileName);
                
                var backupInfo = new BackupInfo
                {
                    CreatedAt = DateTime.Now,
                    DatabaseSize = new FileInfo(_databasePath).Length,
                    BackupType = "Automatic",
                    Version = "1.0"
                };

                await File.WriteAllTextAsync(infoFilePath, System.Text.Json.JsonSerializer.Serialize(backupInfo, new System.Text.Json.JsonSerializerOptions { WriteIndented = true }));

                // تنظيف النسخ القديمة (الاحتفاظ بآخر 10 نسخ)
                await CleanupOldBackupsAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم إنشاء نسخة احتياطية: {backupFileName}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في إنشاء النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        private async Task CleanupOldBackupsAsync()
        {
            try
            {
                var backupFiles = Directory.GetFiles(_backupDirectory, "SFDDatabase_Backup_*.db")
                                           .Select(f => new FileInfo(f))
                                           .OrderByDescending(f => f.CreationTime)
                                           .ToArray();

                if (backupFiles.Length > 10)
                {
                    var filesToDelete = backupFiles.Skip(10);
                    foreach (var file in filesToDelete)
                    {
                        try
                        {
                            File.Delete(file.FullName);

                            // حذف ملف المعلومات المرافق
                            var infoFile = file.FullName.Replace(".db", ".info");
                            if (File.Exists(infoFile))
                            {
                                File.Delete(infoFile);
                            }

                            System.Diagnostics.Debug.WriteLine($"🗑️ تم حذف النسخة الاحتياطية القديمة: {file.Name}");
                        }
                        catch (Exception ex)
                        {
                            System.Diagnostics.Debug.WriteLine($"❌ خطأ في حذف النسخة القديمة {file.Name}: {ex.Message}");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تنظيف النسخ القديمة: {ex.Message}");
            }
        }

        /// <summary>
        /// الحصول على قائمة النسخ الاحتياطية المتاحة
        /// </summary>
        public BackupInfo[] GetAvailableBackups()
        {
            try
            {
                var backupFiles = Directory.GetFiles(_backupDirectory, "SFDDatabase_Backup_*.db")
                                           .Select(f => new FileInfo(f))
                                           .OrderByDescending(f => f.CreationTime)
                                           .ToArray();

                var backups = new List<BackupInfo>();
                
                foreach (var file in backupFiles)
                {
                    var infoFile = file.FullName.Replace(".db", ".info");
                    BackupInfo? info = null;
                    
                    if (File.Exists(infoFile))
                    {
                        try
                        {
                            var json = File.ReadAllText(infoFile);
                            info = System.Text.Json.JsonSerializer.Deserialize<BackupInfo>(json);
                        }
                        catch { }
                    }
                    
                    if (info == null)
                    {
                        info = new BackupInfo
                        {
                            CreatedAt = file.CreationTime,
                            DatabaseSize = file.Length,
                            BackupType = "Unknown",
                            Version = "Unknown"
                        };
                    }
                    
                    info.FileName = file.Name;
                    info.FilePath = file.FullName;
                    backups.Add(info);
                }

                return backups.ToArray();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في جلب قائمة النسخ الاحتياطية: {ex.Message}");
                return Array.Empty<BackupInfo>();
            }
        }

        /// <summary>
        /// استعادة نسخة احتياطية
        /// </summary>
        public async Task<bool> RestoreBackupAsync(string backupFilePath)
        {
            try
            {
                if (!File.Exists(backupFilePath))
                {
                    System.Diagnostics.Debug.WriteLine($"❌ ملف النسخة الاحتياطية غير موجود: {backupFilePath}");
                    return false;
                }

                // إنشاء نسخة احتياطية من قاعدة البيانات الحالية قبل الاستعادة
                await CreateBackupAsync();

                // استعادة النسخة الاحتياطية
                await Task.Run(() => File.Copy(backupFilePath, _databasePath, true));

                System.Diagnostics.Debug.WriteLine($"✅ تم استعادة النسخة الاحتياطية: {Path.GetFileName(backupFilePath)}");
                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في استعادة النسخة الاحتياطية: {ex.Message}");
                return false;
            }
        }

        public void Dispose()
        {
            if (!_isDisposed)
            {
                _backupTimer?.Stop();
                _backupTimer?.Dispose();
                _isDisposed = true;
                System.Diagnostics.Debug.WriteLine("🔄 تم إغلاق خدمة النسخ الاحتياطي");
            }
        }
    }

    /// <summary>
    /// معلومات النسخة الاحتياطية
    /// </summary>
    public class BackupInfo
    {
        public DateTime CreatedAt { get; set; }
        public long DatabaseSize { get; set; }
        public string BackupType { get; set; } = string.Empty;
        public string Version { get; set; } = string.Empty;
        public string FileName { get; set; } = string.Empty;
        public string FilePath { get; set; } = string.Empty;
        
        public string FormattedSize => FormatBytes(DatabaseSize);
        public string FormattedDate => CreatedAt.ToString("dd/MM/yyyy HH:mm:ss");
        
        private static string FormatBytes(long bytes)
        {
            string[] suffixes = { "B", "KB", "MB", "GB" };
            int counter = 0;
            decimal number = bytes;
            while (Math.Round(number / 1024) >= 1)
            {
                number /= 1024;
                counter++;
            }
            return $"{number:n1} {suffixes[counter]}";
        }
    }
}
