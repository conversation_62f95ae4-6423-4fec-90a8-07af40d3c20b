﻿using System;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using DriverManagementSystem.ViewModels;
using DriverManagementSystem.Services;
using DriverManagementSystem.Views;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem;

public partial class MainWindow : Window
{
    private readonly MainViewModel _viewModel;
    private Services.AutoBackupService? _autoBackupService;

    public MainWindow()
    {
        InitializeComponent();

        var authService = new AuthenticationService();
        _viewModel = new MainViewModel(authService);
        DataContext = _viewModel;

        _viewModel.LogoutRequested += OnLogoutRequested;

        // إضافة البيانات الأساسية
        _ = InitializeDatabaseAsync();

        // تشغيل خدمة النسخ الاحتياطي التلقائي
        InitializeAutoBackupService();
    }

    private async Task InitializeDatabaseAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🌱 بدء تهيئة قاعدة البيانات...");

            // التحقق من وجود البيانات أولاً
            using var dataService = new SqliteDataService();
            var existingDrivers = await dataService.GetDriversAsync();
            var existingSectors = await dataService.GetSectorsAsync();
            var existingProjects = await dataService.GetProjectsAsync();

            // إضافة القطاعات فقط إذا لم تكن موجودة
            if (existingSectors.Count == 0)
            {
                var basicSeeder = new DatabaseSeeder();
                await basicSeeder.SeedSectorsAsync();
                basicSeeder.Dispose();
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة القطاعات الأساسية");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"📋 القطاعات موجودة مسبقاً ({existingSectors.Count} قطاع)");
            }

            // تحديث أكواد القطاعات لتتطابق مع Excel
            try
            {
                using var context = new ApplicationDbContext();
                var sectorUpdater = new Migrations.UpdateSectorCodes(context);
                await sectorUpdater.ApplyAsync();
                System.Diagnostics.Debug.WriteLine("✅ تم تحديث أكواد القطاعات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ خطأ في تحديث أكواد القطاعات: {ex.Message}");
            }

            // إضافة المشاريع فقط إذا لم تكن موجودة
            if (existingProjects.Count == 0)
            {
                var basicSeeder = new DatabaseSeeder();
                await basicSeeder.SeedProjectsAsync();
                basicSeeder.Dispose();
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة المشاريع الأساسية");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"📁 المشاريع موجودة مسبقاً ({existingProjects.Count} مشروع)");
            }

            // إضافة البيانات الحقيقية فقط إذا لم تكن موجودة
            if (existingDrivers.Count == 0)
            {
                var realSeeder = new RealDataSeeder();
                await realSeeder.SeedRealDataAsync();
                realSeeder.Dispose();
                System.Diagnostics.Debug.WriteLine("✅ تم إضافة البيانات الحقيقية");
            }
            else
            {
                System.Diagnostics.Debug.WriteLine($"🚗 السائقين موجودين مسبقاً ({existingDrivers.Count} سائق) - لن يتم إعادة الإضافة");
            }

            // اختبار قراءة البيانات النهائية
            var finalSectors = await dataService.GetSectorsAsync();
            var finalOfficers = await dataService.GetOfficersAsync();
            var finalDrivers = await dataService.GetDriversAsync();

            System.Diagnostics.Debug.WriteLine($"📊 إحصائيات قاعدة البيانات النهائية:");
            System.Diagnostics.Debug.WriteLine($"   - القطاعات: {finalSectors.Count}");
            System.Diagnostics.Debug.WriteLine($"   - الموظفين: {finalOfficers.Count}");
            System.Diagnostics.Debug.WriteLine($"   - السائقين: {finalDrivers.Count}");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تهيئة قاعدة البيانات: {ex.Message}");
            System.Diagnostics.Debug.WriteLine($"تفاصيل الخطأ: {ex.StackTrace}");
        }
    }

    private async Task SafeInitializeProfessionalTemplatesAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🎨 بدء تفعيل النماذج الاحترافية بطريقة آمنة...");

            // تفعيل النماذج الاحترافية بدون خدمة خارجية
            await Task.Run(() =>
            {
                try
                {
                    // تفعيل الإعدادات الأساسية
                    DriverManagementSystem.Properties.Settings.Default.ProfessionalTemplatesActivated = true;
                    DriverManagementSystem.Properties.Settings.Default.ActivationDate = DateTime.Now.ToString();
                    DriverManagementSystem.Properties.Settings.Default.Save();

                    System.Diagnostics.Debug.WriteLine("✅ تم تفعيل النماذج الاحترافية بنجاح!");
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في حفظ إعدادات النماذج الاحترافية: {ex.Message}");
                }
            });

            // إظهار رسالة تأكيد للمستخدم
            Dispatcher.BeginInvoke(new Action(() =>
            {
                try
                {
                    MessageBox.Show("🎨 تم تفعيل النماذج الاحترافية بنجاح!\n\n" +
                                   "✅ قوالب الرسائل الاحترافية\n" +
                                   "✅ نماذج إدارة المستخدمين المحسنة\n" +
                                   "✅ قوالب التقارير الاحترافية\n\n" +
                                   "النظام جاهز للاستخدام بالميزات الاحترافية الكاملة!",
                                   "تفعيل النماذج الاحترافية",
                                   MessageBoxButton.OK,
                                   MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    System.Diagnostics.Debug.WriteLine($"❌ خطأ في عرض رسالة التأكيد: {ex.Message}");
                }
            }));
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ عام في تفعيل النماذج الاحترافية: {ex.Message}");
        }
    }

    private async Task InitializeProfessionalTemplatesAsync()
    {
        try
        {
            System.Diagnostics.Debug.WriteLine("🎨 بدء تفعيل النماذج الاحترافية...");

            var templatesService = new ProfessionalTemplatesService();

            // التحقق من حالة التفعيل السابقة
            if (templatesService.AreProfessionalTemplatesActivated())
            {
                System.Diagnostics.Debug.WriteLine("ℹ️ النماذج الاحترافية مفعلة مسبقاً");
                return;
            }

            // تفعيل النماذج الاحترافية
            var success = await templatesService.ActivateAllProfessionalTemplatesAsync();

            if (success)
            {
                System.Diagnostics.Debug.WriteLine("✅ تم تفعيل جميع النماذج الاحترافية بنجاح!");

                // إظهار رسالة تأكيد للمستخدم
                Dispatcher.BeginInvoke(new Action(() =>
                {
                    MessageBox.Show("🎨 تم تفعيل النماذج الاحترافية بنجاح!\n\n" +
                                   "✅ قوالب الرسائل الاحترافية\n" +
                                   "✅ نماذج إدارة المستخدمين المحسنة\n" +
                                   "✅ قوالب التقارير الاحترافية\n\n" +
                                   "النظام جاهز للاستخدام بالميزات الاحترافية الكاملة!",
                                   "تفعيل النماذج الاحترافية",
                                   MessageBoxButton.OK,
                                   MessageBoxImage.Information);
                }));
            }
            else
            {
                System.Diagnostics.Debug.WriteLine("⚠️ فشل في تفعيل بعض النماذج الاحترافية");
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تفعيل النماذج الاحترافية: {ex.Message}");
        }
    }



    private void OnLogoutRequested(object? sender, System.EventArgs e)
    {
        var loginWindow = new Views.LoginWindow();
        loginWindow.Show();
        this.Close();
    }



    /// <summary>
    /// تشغيل خدمة النسخ الاحتياطي التلقائي
    /// </summary>
    private void InitializeAutoBackupService()
    {
        try
        {
            _autoBackupService = new Services.AutoBackupService();
            _autoBackupService.Start();
            System.Diagnostics.Debug.WriteLine("🔄 تم تشغيل خدمة النسخ الاحتياطي التلقائي");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في تشغيل خدمة النسخ الاحتياطي: {ex.Message}");
        }
    }

    protected override void OnClosed(System.EventArgs e)
    {
        try
        {
            _viewModel.LogoutRequested -= OnLogoutRequested;
            _autoBackupService?.Dispose();
            System.Diagnostics.Debug.WriteLine("✅ تم إغلاق جميع الخدمات بنجاح");
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"❌ خطأ في إغلاق الخدمات: {ex.Message}");
        }
        finally
        {
            base.OnClosed(e);
        }
    }
}