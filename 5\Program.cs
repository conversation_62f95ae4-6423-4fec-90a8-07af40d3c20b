using System;
using System.Threading.Tasks;
using System.Windows;
using DriverManagementSystem.Services;

namespace DriverManagementSystem
{
    public class Program
    {
        [STAThread]
        public static void Main(string[] args)
        {
            try
            {
                // التحقق من وجود معامل لإضافة البيانات
                if (args.Length > 0 && args[0] == "--add-drivers")
                {
                    AddDriversData().GetAwaiter().GetResult();
                    return;
                }

                // تشغيل النظام العادي
                var app = new App();
                app.InitializeComponent();
                app.Run();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تشغيل النظام: {ex.Message}\n\nتفاصيل الخطأ:\n{ex.ToString()}",
                    "خطأ في النظام", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private static async Task AddDriversData()
        {
            try
            {
                Console.WriteLine("🔄 بدء إضافة بيانات السائقين والمركبات والمشاريع...");
                
                var seeder = new DatabaseSeeder();
                await seeder.SeedAllDataAsync();
                
                Console.WriteLine("✅ تم إضافة جميع البيانات بنجاح!");
                Console.WriteLine();
                Console.WriteLine("البيانات المضافة:");
                Console.WriteLine("- 36 سائق مع بياناتهم الكاملة");
                Console.WriteLine("- 36 مركبة مرتبطة بالسائقين");
                Console.WriteLine("- 95 مشروع مع أرقامها وأسمائها");
                Console.WriteLine("- 15 قطاع مختلف");
                Console.WriteLine();
                Console.WriteLine("أسماء الجداول:");
                Console.WriteLine("- Drivers (السائقين)");
                Console.WriteLine("- Vehicles (المركبات)");
                Console.WriteLine("- Projects (المشاريع)");
                Console.WriteLine("- Sectors (القطاعات)");
                Console.WriteLine();
                Console.WriteLine("مسار قاعدة البيانات:");
                Console.WriteLine("%AppData%\\SFDSYS\\SFDSYS.db");
                Console.WriteLine();
                Console.WriteLine("يمكنك الآن تشغيل النظام الرئيسي لعرض البيانات");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ خطأ في إضافة البيانات: {ex.Message}");
                Console.WriteLine();
                Console.WriteLine("تفاصيل الخطأ:");
                Console.WriteLine(ex.ToString());
            }
        }
    }
}
