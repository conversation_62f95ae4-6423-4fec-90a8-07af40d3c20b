<Window x:Class="SFDSystem.Views.ProfessionalMessagesWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:local="clr-namespace:SFDSystem.Views"
        Title="نظام إرسال الرسائل للسائقين"
        Height="850" Width="1300"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        Background="White"
        WindowState="Maximized">

    <Window.Resources>
        <!-- Modern Card Style -->
        <Style x:Key="ModernCard" TargetType="Border">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="15"/>
            <Setter Property="Padding" Value="25"/>
            <Setter Property="Margin" Value="10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="#E0E0E0" Direction="270" ShadowDepth="4" BlurRadius="15" Opacity="0.3"/>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Professional Button Style -->
        <Style x:Key="ProfessionalButton" TargetType="Button">
            <Setter Property="Background" Value="#6C757D"/>
            <Setter Property="Foreground" Value="White"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Padding" Value="20,12"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontWeight" Value="SemiBold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="10"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               BorderBrush="{TemplateBinding BorderBrush}">
                            <ContentPresenter HorizontalAlignment="Center"
                                            VerticalAlignment="Center"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Background" Value="#5A6268"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Background" Value="#495057"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Success Button -->
        <Style x:Key="SuccessButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#28A745"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#218838"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Danger Button -->
        <Style x:Key="DangerButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#DC3545"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#C82333"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Warning Button -->
        <Style x:Key="WarningButton" TargetType="Button" BasedOn="{StaticResource ProfessionalButton}">
            <Setter Property="Background" Value="#FFC107"/>
            <Setter Property="Foreground" Value="#212529"/>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="#E0A800"/>
                </Trigger>
            </Style.Triggers>
        </Style>

        <!-- Modern TextBox Style -->
        <Style x:Key="ModernTextBox" TargetType="TextBox">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="TextBox">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="8">
                            <ScrollViewer x:Name="PART_ContentHost"
                                        Padding="{TemplateBinding Padding}"/>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsFocused" Value="True">
                                <Setter Property="BorderBrush" Value="#6C757D"/>
                                <Setter Property="Background" Value="White"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <!-- Modern ComboBox Style -->
        <Style x:Key="ModernComboBox" TargetType="ComboBox">
            <Setter Property="Background" Value="#F8F9FA"/>
            <Setter Property="BorderBrush" Value="#DEE2E6"/>
            <Setter Property="BorderThickness" Value="1"/>
            <Setter Property="Padding" Value="12"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="ComboBox">
                        <Border Background="{TemplateBinding Background}"
                               BorderBrush="{TemplateBinding BorderBrush}"
                               BorderThickness="{TemplateBinding BorderThickness}"
                               CornerRadius="8">
                            <Grid>
                                <ToggleButton x:Name="ToggleButton" 
                                            Background="Transparent"
                                            BorderThickness="0"
                                            IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}"
                                            ClickMode="Press"/>
                                <ContentPresenter x:Name="ContentSite"
                                                IsHitTestVisible="False"
                                                Content="{TemplateBinding SelectionBoxItem}"
                                                ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                Margin="{TemplateBinding Padding}"
                                                VerticalAlignment="Center"
                                                HorizontalAlignment="Left"/>
                                <Popup x:Name="Popup"
                                     Placement="Bottom"
                                     IsOpen="{TemplateBinding IsDropDownOpen}"
                                     AllowsTransparency="True"
                                     Focusable="False"
                                     PopupAnimation="Slide">
                                    <Grid x:Name="DropDown"
                                        SnapsToDevicePixels="True"
                                        MinWidth="{TemplateBinding ActualWidth}"
                                        MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                        <Border x:Name="DropDownBorder"
                                              Background="White"
                                              BorderThickness="1"
                                              BorderBrush="#DEE2E6"
                                              CornerRadius="8"/>
                                        <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                            <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained"/>
                                        </ScrollViewer>
                                    </Grid>
                                </Popup>
                            </Grid>
                        </Border>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <!-- Main Content -->
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- Simple Header -->
        <Border Grid.Row="0" Background="White" Height="60" BorderBrush="#E0E0E0" BorderThickness="0,0,0,1">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="Auto"/>
                </Grid.ColumnDefinitions>

                <!-- Title -->
                <TextBlock Grid.Column="0" Text="نظام إرسال الرسائل للسائقين"
                          FontSize="18" FontWeight="Bold"
                          Foreground="#333333"
                          VerticalAlignment="Center"
                          Margin="30,0"/>

                <!-- Visit Number and Duration -->
                <StackPanel Grid.Column="1" Orientation="Horizontal" VerticalAlignment="Center" Margin="20,0">
                    <TextBlock Text="رقم الزيارة:" FontSize="14" FontWeight="SemiBold"
                              Foreground="#666666" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding VisitNumber, FallbackValue='15'}" FontSize="14" FontWeight="Bold"
                              Foreground="#333333" Margin="0,0,20,0"/>

                    <TextBlock Text="المدة:" FontSize="14" FontWeight="SemiBold"
                              Foreground="#666666" Margin="0,0,5,0"/>
                    <TextBlock Text="{Binding CorrectDuration, FallbackValue='3 أيام'}" FontSize="14" FontWeight="Bold"
                              Foreground="#333333" Margin="0,0,30,0"/>
                </StackPanel>
            </Grid>
        </Border>

        <!-- Main Content Area -->
        <Grid Grid.Row="1" Margin="15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="850"/>
                <ColumnDefinition Width="15"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- Left Panel - Driver Selection -->
            <Border Grid.Column="0" Style="{StaticResource ModernCard}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>

                    <!-- Clean Header -->
                    <Grid Grid.Row="0" Margin="0,0,0,20">
                        <StackPanel>
                            <TextBlock Text="👥 اختيار السائقين" FontSize="20" FontWeight="Bold"
                                      Foreground="#2C3E50" HorizontalAlignment="Center" Margin="0,0,0,15"/>

                            <!-- Vehicle Filter Buttons - Ultra Professional -->
                            <WrapPanel HorizontalAlignment="Center" Orientation="Horizontal">
                                <CheckBox x:Name="ForshanalFilter" IsChecked="{Binding IsForshanalSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                         Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                         Margin="4" FontSize="11" VerticalAlignment="Center">
                                    <Border Background="#007BFF" CornerRadius="10" Padding="12,8">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Direction="320" ShadowDepth="2" Opacity="0.4"/>
                                        </Border.Effect>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚐" FontSize="14" Margin="0,0,6,0" Foreground="White"/>
                                            <TextBlock Text="فورشنال" Foreground="White" FontWeight="Bold" FontSize="12"/>
                                        </StackPanel>
                                    </Border>
                                </CheckBox>

                                <CheckBox x:Name="KanterFilter" IsChecked="{Binding IsKanterSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                         Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                         Margin="4" FontSize="11" VerticalAlignment="Center">
                                    <Border Background="#28A745" CornerRadius="10" Padding="12,8">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Direction="320" ShadowDepth="2" Opacity="0.4"/>
                                        </Border.Effect>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚚" FontSize="14" Margin="0,0,6,0" Foreground="White"/>
                                            <TextBlock Text="كنتر" Foreground="White" FontWeight="Bold" FontSize="12"/>
                                        </StackPanel>
                                    </Border>
                                </CheckBox>

                                <CheckBox x:Name="HiluxFilter" IsChecked="{Binding IsHiluxSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                         Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                         Margin="4" FontSize="11" VerticalAlignment="Center">
                                    <Border Background="#FFC107" CornerRadius="10" Padding="12,8">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Direction="320" ShadowDepth="2" Opacity="0.4"/>
                                        </Border.Effect>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚙" FontSize="14" Margin="0,0,6,0" Foreground="White"/>
                                            <TextBlock Text="هيلوكس" Foreground="White" FontWeight="Bold" FontSize="12"/>
                                        </StackPanel>
                                    </Border>
                                </CheckBox>

                                <CheckBox x:Name="BusFilter" IsChecked="{Binding IsBusSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                         Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                         Margin="4" FontSize="11" VerticalAlignment="Center">
                                    <Border Background="#DC3545" CornerRadius="10" Padding="12,8">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Direction="320" ShadowDepth="2" Opacity="0.4"/>
                                        </Border.Effect>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚌" FontSize="14" Margin="0,0,6,0" Foreground="White"/>
                                            <TextBlock Text="حافلة" Foreground="White" FontWeight="Bold" FontSize="12"/>
                                        </StackPanel>
                                    </Border>
                                </CheckBox>

                                <CheckBox x:Name="PradoFilter" IsChecked="{Binding IsPradoSelected, Mode=TwoWay, UpdateSourceTrigger=PropertyChanged}"
                                         Checked="VehicleFilter_Changed" Unchecked="VehicleFilter_Changed"
                                         Margin="4" FontSize="11" VerticalAlignment="Center">
                                    <Border Background="#6F42C1" CornerRadius="10" Padding="12,8">
                                        <Border.Effect>
                                            <DropShadowEffect Color="Black" Direction="320" ShadowDepth="2" Opacity="0.4"/>
                                        </Border.Effect>
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="🚗" FontSize="14" Margin="0,0,6,0" Foreground="White"/>
                                            <TextBlock Text="برادو" Foreground="White" FontWeight="Bold" FontSize="12"/>
                                        </StackPanel>
                                    </Border>
                                </CheckBox>
                            </WrapPanel>


                        </StackPanel>
                    </Grid>



                    <!-- Professional Drivers DataGrid -->
                    <Border Grid.Row="1" Background="White" CornerRadius="15" Padding="0" Margin="0,0,0,15">
                        <Border.Effect>
                            <DropShadowEffect Color="Gray" Direction="320" ShadowDepth="3" Opacity="0.3"/>
                        </Border.Effect>

                        <DataGrid ItemsSource="{Binding FilteredDrivers}"
                                 SelectedItem="{Binding SelectedDriver}"
                                 AutoGenerateColumns="False"
                                 CanUserAddRows="False"
                                 CanUserDeleteRows="False"
                                 GridLinesVisibility="None"
                                 HeadersVisibility="Column"
                                 Background="Transparent"
                                 BorderThickness="0"
                                 AlternatingRowBackground="#F8F9FA"
                                 FontSize="13"
                                 RowHeight="28"
                                 VerticalScrollBarVisibility="Visible"
                                 HorizontalScrollBarVisibility="Auto"
                                 ScrollViewer.CanContentScroll="False"
                                 ScrollViewer.VerticalScrollBarVisibility="Visible"
                                 ScrollViewer.HorizontalScrollBarVisibility="Auto">

                            <!-- Professional Header Style -->
                            <DataGrid.ColumnHeaderStyle>
                                <Style TargetType="DataGridColumnHeader">
                                    <Setter Property="Background">
                                        <Setter.Value>
                                            <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                                <GradientStop Color="#495057" Offset="0"/>
                                                <GradientStop Color="#6C757D" Offset="1"/>
                                            </LinearGradientBrush>
                                        </Setter.Value>
                                    </Setter>
                                    <Setter Property="Foreground" Value="White"/>
                                    <Setter Property="FontWeight" Value="Bold"/>
                                    <Setter Property="FontSize" Value="13"/>
                                    <Setter Property="Height" Value="45"/>
                                    <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                    <Setter Property="VerticalContentAlignment" Value="Center"/>
                                    <Setter Property="BorderBrush" Value="#DEE2E6"/>
                                    <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                </Style>
                            </DataGrid.ColumnHeaderStyle>

                            <!-- Professional Row Style -->
                            <DataGrid.RowStyle>
                                <Style TargetType="DataGridRow">
                                    <Setter Property="Background" Value="White"/>
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsMouseOver" Value="True">
                                            <Setter Property="Background" Value="#E3F2FD"/>
                                        </Trigger>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="#BBDEFB"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.RowStyle>

                            <!-- Professional Cell Style -->
                            <DataGrid.CellStyle>
                                <Style TargetType="DataGridCell">
                                    <Setter Property="BorderThickness" Value="0"/>
                                    <Setter Property="VerticalAlignment" Value="Center"/>
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="Padding" Value="8,5"/>
                                    <Style.Triggers>
                                        <Trigger Property="IsSelected" Value="True">
                                            <Setter Property="Background" Value="Transparent"/>
                                            <Setter Property="Foreground" Value="#212529"/>
                                        </Trigger>
                                    </Style.Triggers>
                                </Style>
                            </DataGrid.CellStyle>

                            <DataGrid.Columns>
                                <DataGridCheckBoxColumn Header="✓" Binding="{Binding IsSelected}" Width="60"/>
                                <DataGridTextColumn Header="كود السائق" Binding="{Binding DriverCode}" Width="100"/>
                                <DataGridTextColumn Header="اسم السائق" Binding="{Binding Name}" Width="*"/>
                                <DataGridTextColumn Header="رقم الهاتف" Binding="{Binding PhoneNumber}" Width="140"/>
                                <DataGridTextColumn Header="نوع المركبة" Binding="{Binding VehicleType}" Width="110"/>
                                <DataGridTextColumn Header="الحالة" Binding="{Binding Status}" Width="90"/>
                            </DataGrid.Columns>
                        </DataGrid>
                    </Border>

                    <!-- Offers Results Section -->
                    <Border Grid.Row="2" Background="#F8F9FA" CornerRadius="10" Padding="15" Margin="0,0,0,15">
                        <Grid>
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="*"/>
                            </Grid.RowDefinitions>

                            <TextBlock Grid.Row="0" Text="🏆 السائقين الذين تم تقديم أسعارهم"
                                      FontSize="16" FontWeight="Bold" Foreground="#2C3E50"
                                      HorizontalAlignment="Center" Margin="0,0,0,10"/>

                            <DataGrid Grid.Row="1" x:Name="OffersResultsGrid"
                                     ItemsSource="{Binding OffersResults}"
                                     AutoGenerateColumns="False"
                                     CanUserAddRows="False"
                                     CanUserDeleteRows="False"
                                     GridLinesVisibility="None"
                                     HeadersVisibility="Column"
                                     Background="White"
                                     BorderThickness="1"
                                     BorderBrush="#DEE2E6"
                                     AlternatingRowBackground="#F8F9FA"
                                     FontSize="12"
                                     RowHeight="35"
                                     MinHeight="150"
                                     VerticalScrollBarVisibility="Auto"
                                     HorizontalScrollBarVisibility="Auto">

                                <DataGrid.ColumnHeaderStyle>
                                    <Style TargetType="DataGridColumnHeader">
                                        <Setter Property="Background" Value="#6C757D"/>
                                        <Setter Property="Foreground" Value="White"/>
                                        <Setter Property="FontWeight" Value="Bold"/>
                                        <Setter Property="FontSize" Value="12"/>
                                        <Setter Property="Height" Value="35"/>
                                        <Setter Property="HorizontalContentAlignment" Value="Center"/>
                                        <Setter Property="VerticalContentAlignment" Value="Center"/>
                                        <Setter Property="BorderBrush" Value="#DEE2E6"/>
                                        <Setter Property="BorderThickness" Value="0,0,1,1"/>
                                    </Style>
                                </DataGrid.ColumnHeaderStyle>

                                <DataGrid.RowStyle>
                                    <Style TargetType="DataGridRow">
                                        <Setter Property="Background" Value="White"/>
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsMouseOver" Value="True">
                                                <Setter Property="Background" Value="#E3F2FD"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.RowStyle>

                                <DataGrid.CellStyle>
                                    <Style TargetType="DataGridCell">
                                        <Setter Property="BorderThickness" Value="0"/>
                                        <Setter Property="VerticalAlignment" Value="Center"/>
                                        <Setter Property="HorizontalAlignment" Value="Center"/>
                                        <Setter Property="Padding" Value="8,5"/>
                                        <Style.Triggers>
                                            <Trigger Property="IsSelected" Value="True">
                                                <Setter Property="Background" Value="Transparent"/>
                                                <Setter Property="Foreground" Value="#212529"/>
                                            </Trigger>
                                        </Style.Triggers>
                                    </Style>
                                </DataGrid.CellStyle>

                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="اسم السائق" Binding="{Binding DriverName}" Width="150"/>
                                    <DataGridTextColumn Header="السعر المقدم" Binding="{Binding OfferedPrice}" Width="120"/>

                                    <!-- عمود الحالة بسيط وجميل -->
                                    <DataGridTemplateColumn Header="الحالة" Width="100">
                                        <DataGridTemplateColumn.CellTemplate>
                                            <DataTemplate>
                                                <TextBlock Text="{Binding Status}"
                                                          FontWeight="Bold"
                                                          FontSize="11"
                                                          HorizontalAlignment="Center"
                                                          VerticalAlignment="Center"
                                                          Cursor="Hand"
                                                          MouseLeftButtonDown="ToggleWinnerStatus_Click"
                                                          Tag="{Binding}"
                                                          ToolTip="اضغط لتغيير الحالة"/>
                                            </DataTemplate>
                                        </DataGridTemplateColumn.CellTemplate>
                                    </DataGridTemplateColumn>

                                    <DataGridTextColumn Header="تاريخ التقديم" Binding="{Binding SubmissionDate}" Width="120"/>
                                    <DataGridTextColumn Header="📱 نص الرسالة" Binding="{Binding MessageText}" Width="*">
                                        <DataGridTextColumn.ElementStyle>
                                            <Style TargetType="TextBlock">
                                                <Setter Property="TextWrapping" Value="Wrap"/>
                                                <Setter Property="MaxWidth" Value="300"/>
                                                <Setter Property="ToolTip" Value="{Binding MessageText}"/>
                                                <Setter Property="Foreground" Value="#2196F3"/>
                                                <Setter Property="FontSize" Value="11"/>
                                            </Style>
                                        </DataGridTextColumn.ElementStyle>
                                    </DataGridTextColumn>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>
                    </Border>

                    <!-- Action Buttons -->
                    <Grid Grid.Row="3" Margin="0,0,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="6"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="6"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="6"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" x:Name="OffersButton" Click="OffersButton_Click"
                               Background="#FF9800" Foreground="White" FontWeight="Bold"
                               FontSize="12" Height="45"
                               BorderThickness="0" Cursor="Hand">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="8"
                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                       BorderBrush="{TemplateBinding BorderBrush}">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#E68900"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#CC7A00"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <Button.Effect>
                                <DropShadowEffect Color="Black" Direction="320" ShadowDepth="1" Opacity="0.2"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🏆" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="عروض الأسعار" FontSize="12"/>
                            </StackPanel>
                        </Button>

                        <!-- Edit Offers Button - Professional Design -->
                        <Button Grid.Column="2" x:Name="EditOffersButton" Click="EditOffersButton_Click"
                               Background="#6C5CE7" Foreground="White" FontWeight="Bold"
                               FontSize="12" Height="45"
                               BorderThickness="0" Cursor="Hand"
                               ToolTip="تعديل عروض الأسعار المحفوظة للزيارة المحددة">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="10"
                                                       BorderThickness="2"
                                                       BorderBrush="#5A4FCF">
                                                    <Grid>
                                                        <Border x:Name="GlowBorder"
                                                               CornerRadius="10"
                                                               Opacity="0">
                                                            <Border.Background>
                                                                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                                                                    <GradientStop Color="#FFFFFF" Offset="0" />
                                                                    <GradientStop Color="Transparent" Offset="1" />
                                                                </LinearGradientBrush>
                                                            </Border.Background>
                                                        </Border>
                                                        <ContentPresenter HorizontalAlignment="Center"
                                                                        VerticalAlignment="Center"/>
                                                    </Grid>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#5A4FCF"/>
                                                        <Setter TargetName="GlowBorder" Property="Opacity" Value="0.2"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#4834D4"/>
                                                        <Setter TargetName="GlowBorder" Property="Opacity" Value="0.4"/>
                                                    </Trigger>
                                                    <Trigger Property="IsEnabled" Value="False">
                                                        <Setter Property="Background" Value="#CCCCCC"/>
                                                        <Setter Property="Foreground" Value="#666666"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <Button.Effect>
                                <DropShadowEffect Color="#6C5CE7" Direction="270" ShadowDepth="2" Opacity="0.3" BlurRadius="8"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <Border Background="#FFFFFF" CornerRadius="12" Width="24" Height="24" Margin="0,0,8,0">
                                    <TextBlock Text="✏️" FontSize="12" HorizontalAlignment="Center" VerticalAlignment="Center"/>
                                </Border>
                                <StackPanel Orientation="Vertical">
                                    <TextBlock Text="تعديل العروض" FontSize="11" FontWeight="Bold"/>
                                    <TextBlock Text="تحرير البيانات المحفوظة" FontSize="8" Opacity="0.8"/>
                                </StackPanel>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="4" x:Name="SaveOffersButton" Click="SaveOffersButton_Click"
                               Background="#28A745" Foreground="White" FontWeight="Bold"
                               FontSize="12" Height="45"
                               BorderThickness="0" Cursor="Hand">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="8"
                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                       BorderBrush="{TemplateBinding BorderBrush}">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#218838"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#1E7E34"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <Button.Effect>
                                <DropShadowEffect Color="Black" Direction="320" ShadowDepth="1" Opacity="0.2"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="💾" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="حفظ" FontSize="12"/>
                            </StackPanel>
                        </Button>

                        <Button Grid.Column="6" x:Name="ClearOffersButton" Click="ClearOffersButton_Click"
                               Background="#DC3545" Foreground="White" FontWeight="Bold"
                               FontSize="12" Height="45"
                               BorderThickness="0" Cursor="Hand">
                            <Button.Style>
                                <Style TargetType="Button">
                                    <Setter Property="Template">
                                        <Setter.Value>
                                            <ControlTemplate TargetType="Button">
                                                <Border Background="{TemplateBinding Background}"
                                                       CornerRadius="8"
                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                       BorderBrush="{TemplateBinding BorderBrush}">
                                                    <ContentPresenter HorizontalAlignment="Center"
                                                                    VerticalAlignment="Center"/>
                                                </Border>
                                                <ControlTemplate.Triggers>
                                                    <Trigger Property="IsMouseOver" Value="True">
                                                        <Setter Property="Background" Value="#C82333"/>
                                                    </Trigger>
                                                    <Trigger Property="IsPressed" Value="True">
                                                        <Setter Property="Background" Value="#BD2130"/>
                                                    </Trigger>
                                                </ControlTemplate.Triggers>
                                            </ControlTemplate>
                                        </Setter.Value>
                                    </Setter>
                                </Style>
                            </Button.Style>
                            <Button.Effect>
                                <DropShadowEffect Color="Black" Direction="320" ShadowDepth="1" Opacity="0.2"/>
                            </Button.Effect>
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🗑️" FontSize="14" Margin="0,0,6,0"/>
                                <TextBlock Text="مسح النتائج" FontSize="12"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </Border>

            <!-- Right Panel - Message Content -->
            <Border Grid.Column="2" Style="{StaticResource ModernCard}">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>





                    <!-- Message Content -->
                    <Grid Grid.Row="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="Auto"/>
                            <RowDefinition Height="*"/>
                        </Grid.RowDefinitions>

                        <TextBlock Grid.Row="0" Text="📄 محتوى الرسالة الاحترافية" FontSize="16" FontWeight="Bold"
                                  Foreground="#495057" Margin="0,0,0,10" HorizontalAlignment="Center"/>

                        <!-- Message Templates -->
                        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,15">
                            <TextBlock Text="📋 قالب الرسالة:" FontSize="13" FontWeight="SemiBold"
                                      Foreground="#495057" VerticalAlignment="Center" Margin="0,0,15,0"/>

                            <ComboBox Height="35" Width="250" FontSize="13" Padding="10,8"
                                     ItemsSource="{Binding MessageTemplates}"
                                     SelectedItem="{Binding SelectedMessageTemplate}"
                                     DisplayMemberPath="Name"
                                     Background="White"
                                     Foreground="#333333"
                                     BorderBrush="#CCCCCC"
                                     BorderThickness="1"
                                     SelectionChanged="MessageTemplate_SelectionChanged">
                                <ComboBox.Style>
                                    <Style TargetType="ComboBox">
                                        <Setter Property="Template">
                                            <Setter.Value>
                                                <ControlTemplate TargetType="ComboBox">
                                                    <Grid>
                                                        <ToggleButton Name="ToggleButton"
                                                                     Background="{TemplateBinding Background}"
                                                                     BorderBrush="{TemplateBinding BorderBrush}"
                                                                     BorderThickness="{TemplateBinding BorderThickness}"
                                                                     Grid.Column="2"
                                                                     Focusable="false"
                                                                     IsChecked="{Binding Path=IsDropDownOpen,Mode=TwoWay,RelativeSource={RelativeSource TemplatedParent}}"
                                                                     ClickMode="Press">
                                                            <ToggleButton.Style>
                                                                <Style TargetType="ToggleButton">
                                                                    <Setter Property="Template">
                                                                        <Setter.Value>
                                                                            <ControlTemplate TargetType="ToggleButton">
                                                                                <Border Background="{TemplateBinding Background}"
                                                                                       BorderBrush="{TemplateBinding BorderBrush}"
                                                                                       BorderThickness="{TemplateBinding BorderThickness}"
                                                                                       CornerRadius="4">
                                                                                    <Grid>
                                                                                        <Grid.ColumnDefinitions>
                                                                                            <ColumnDefinition />
                                                                                            <ColumnDefinition Width="20" />
                                                                                        </Grid.ColumnDefinitions>
                                                                                        <Path Grid.Column="1"
                                                                                             HorizontalAlignment="Center"
                                                                                             VerticalAlignment="Center"
                                                                                             Data="M 0 0 L 4 4 L 8 0 Z"
                                                                                             Fill="#666666"/>
                                                                                    </Grid>
                                                                                </Border>
                                                                            </ControlTemplate>
                                                                        </Setter.Value>
                                                                    </Setter>
                                                                </Style>
                                                            </ToggleButton.Style>
                                                        </ToggleButton>
                                                        <ContentPresenter Name="ContentSite"
                                                                         IsHitTestVisible="False"
                                                                         Content="{TemplateBinding SelectionBoxItem}"
                                                                         ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}"
                                                                         ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}"
                                                                         Margin="10,8,30,8"
                                                                         VerticalAlignment="Center"
                                                                         HorizontalAlignment="Left" />
                                                        <TextBox x:Name="PART_EditableTextBox"
                                                                Style="{x:Null}"
                                                                Template="{DynamicResource ComboBoxTextBox}"
                                                                HorizontalAlignment="Left"
                                                                VerticalAlignment="Center"
                                                                Margin="3,3,23,3"
                                                                Focusable="True"
                                                                Background="Transparent"
                                                                Visibility="Hidden"
                                                                IsReadOnly="{TemplateBinding IsReadOnly}"/>
                                                        <Popup Name="Popup"
                                                              Placement="Bottom"
                                                              IsOpen="{TemplateBinding IsDropDownOpen}"
                                                              AllowsTransparency="True"
                                                              Focusable="False"
                                                              PopupAnimation="Slide">
                                                            <Grid Name="DropDown"
                                                                 SnapsToDevicePixels="True"
                                                                 MinWidth="{TemplateBinding ActualWidth}"
                                                                 MaxHeight="{TemplateBinding MaxDropDownHeight}">
                                                                <Border x:Name="DropDownBorder"
                                                                       Background="White"
                                                                       BorderThickness="1"
                                                                       BorderBrush="#CCCCCC"
                                                                       CornerRadius="4"/>
                                                                <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True">
                                                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />
                                                                </ScrollViewer>
                                                            </Grid>
                                                        </Popup>
                                                    </Grid>
                                                </ControlTemplate>
                                            </Setter.Value>
                                        </Setter>
                                    </Style>
                                </ComboBox.Style>
                            </ComboBox>
                        </StackPanel>

                        <!-- Send Options -->
                        <StackPanel Grid.Row="2" Orientation="Vertical" HorizontalAlignment="Center" Margin="0,0,0,10">
                            <!-- Send Method Selection -->
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,0,0,10">
                                <TextBlock Text="📤 طريقة الإرسال:" FontSize="12" FontWeight="SemiBold"
                                          Foreground="#495057" VerticalAlignment="Center" Margin="0,0,15,0"/>

                                <!-- SMS Option -->
                                <RadioButton GroupName="SendMethod" IsChecked="{Binding IsSMSSelected}"
                                            Margin="0,0,20,0" Cursor="Hand"
                                            MouseLeftButtonDown="SendOption_Click" Tag="SMS">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📱" FontSize="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="SMS" FontSize="12" FontWeight="SemiBold" Foreground="#17A2B8"/>
                                    </StackPanel>
                                </RadioButton>

                                <!-- WhatsApp Option -->
                                <RadioButton GroupName="SendMethod" IsChecked="{Binding IsWhatsAppSelected}"
                                            Margin="0,0,20,0" Cursor="Hand"
                                            MouseLeftButtonDown="SendOption_Click" Tag="WhatsApp">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="💬" FontSize="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="WhatsApp" FontSize="12" FontWeight="SemiBold" Foreground="#25D366"/>
                                    </StackPanel>
                                </RadioButton>

                                <!-- Email Option -->
                                <RadioButton GroupName="SendMethod" IsChecked="{Binding IsEmailSelected}"
                                            Cursor="Hand"
                                            MouseLeftButtonDown="SendOption_Click" Tag="Email">
                                    <StackPanel Orientation="Horizontal">
                                        <TextBlock Text="📧" FontSize="16" Margin="0,0,5,0"/>
                                        <TextBlock Text="Email" FontSize="12" FontWeight="SemiBold" Foreground="#6C757D"/>
                                    </StackPanel>
                                </RadioButton>
                            </StackPanel>

                            <!-- WhatsApp Options (only visible when WhatsApp is selected) -->
                            <StackPanel Orientation="Vertical" HorizontalAlignment="Center"
                                       Visibility="{Binding IsWhatsAppSelected, Converter={StaticResource BooleanToVisibilityConverter}}">

                                <!-- Sending Mode -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5">
                                    <TextBlock Text="🤖 نمط الإرسال:" FontSize="11" FontWeight="SemiBold"
                                              Foreground="#495057" VerticalAlignment="Center" Margin="0,0,10,0"/>

                                    <CheckBox IsChecked="{Binding UseAutomaticSending}"
                                             Content="إرسال تلقائي" FontSize="11"
                                             Foreground="#25D366" FontWeight="SemiBold"
                                             ToolTip="تفعيل الإرسال التلقائي - سيقوم النظام بالضغط على زر الإرسال تلقائياً"/>

                                    <TextBlock Text="(يدوي إذا لم يُفعل)" FontSize="10"
                                              Foreground="#6C757D" VerticalAlignment="Center" Margin="5,0,0,0"/>
                                </StackPanel>

                                <!-- Send Type -->
                                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,5">
                                    <TextBlock Text="📤 نوع الإرسال:" FontSize="11" FontWeight="SemiBold"
                                              Foreground="#495057" VerticalAlignment="Center" Margin="0,0,15,0"/>

                                    <RadioButton GroupName="SendType" IsChecked="{Binding IsIndividualSending}"
                                                Margin="0,0,15,0" Cursor="Hand">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="👤" FontSize="14" Margin="0,0,5,0"/>
                                            <TextBlock Text="فردي" FontSize="11" FontWeight="SemiBold" Foreground="#007BFF"/>
                                        </StackPanel>
                                    </RadioButton>

                                    <RadioButton GroupName="SendType" IsChecked="{Binding IsGroupSending}"
                                                Cursor="Hand">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="👥" FontSize="14" Margin="0,0,5,0"/>
                                            <TextBlock Text="جماعي" FontSize="11" FontWeight="SemiBold" Foreground="#28A745"/>
                                        </StackPanel>
                                    </RadioButton>
                                </StackPanel>
                            </StackPanel>
                        </StackPanel>

                        <TextBox Grid.Row="3" Text="{Binding MessageContent, UpdateSourceTrigger=PropertyChanged}"
                                Style="{StaticResource ModernTextBox}"
                                TextWrapping="Wrap"
                                AcceptsReturn="True"
                                MinHeight="400"
                                VerticalScrollBarVisibility="Auto"
                                FontFamily="Segoe UI" FontSize="13"
                                Margin="0,5,0,0"/>
                    </Grid>

                    <!-- Send Actions -->
                    <Grid Grid.Row="1" Margin="0,15,0,0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="15"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <Button Grid.Column="0" Style="{StaticResource WarningButton}" Height="35" FontSize="11"
                               Click="CopyMessage_Click">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="📋" FontSize="12" Margin="0,0,4,0"/>
                                <TextBlock Text="نسخ الرسالة"/>
                            </StackPanel>
                        </Button>

                        <!-- Unified Send Button -->
                        <Button Grid.Column="2" Grid.ColumnSpan="3" Style="{StaticResource SuccessButton}"
                               Height="40" FontSize="12" FontWeight="Bold"
                               Command="{Binding SendMessageCommand}"
                               HorizontalAlignment="Center" MinWidth="150">
                            <StackPanel Orientation="Horizontal">
                                <TextBlock Text="🚀" FontSize="14" Margin="0,0,8,0"/>
                                <TextBlock Text="إرسال" FontWeight="Bold"/>
                            </StackPanel>
                        </Button>
                    </Grid>
                </Grid>
            </Border>
        </Grid>
    </Grid>
</Window>
