using System.ComponentModel;

namespace DriverManagementSystem.Models
{
    /// <summary>
    /// نموذج يوم خط السير - للواجهة والعرض
    /// </summary>
    public class ItineraryDay : INotifyPropertyChanged
    {
        private int _dayNumber;
        private string _itinerary = string.Empty;

        public int DayNumber
        {
            get => _dayNumber;
            set
            {
                _dayNumber = value;
                OnPropertyChanged(nameof(DayNumber));
            }
        }

        public string Itinerary
        {
            get => _itinerary;
            set
            {
                _itinerary = value;
                OnPropertyChanged(nameof(Itinerary));
            }
        }

        public string DayLabel => $"اليوم {DayNumber}";

        public event PropertyChangedEventHandler? PropertyChanged;

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    // تم نقل FieldVisitItinerary إلى ملف منفصل احترافي
}
