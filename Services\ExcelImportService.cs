
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using ClosedXML.Excel;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Models;
using DriverManagementSystem.Extensions;
using DriverManagementSystem.Data;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة استيراد البيانات من ملفات Excel (ClosedXML)
    /// </summary>
    public class ExcelImportService
    {
        private const int MAX_ITEMS = 50; // زيادة الحد الأقصى لاستيعاب جميع الصفوف
        private readonly IDataService _dataService;

        public ExcelImportService()
        {
            _dataService = new DataService();
        }

        /// <summary>
        /// استيراد بيانات الزيارة من ملف Excel مع التحقق من الصحة
        /// </summary>
        public async Task<FieldVisitImportResult> ImportFieldVisitFromExcel(string filePath, string indexValue)
        {
            var result = new FieldVisitImportResult();
            try
            {
                if (!File.Exists(filePath))
                {
                    result.ErrorMessage = $"الملف غير موجود: {filePath}";
                    return result;
                }

                using var wb = new XLWorkbook(filePath);

                // طباعة أسماء جميع الشيتات للتشخيص
                System.Diagnostics.Debug.WriteLine("🔍 أسماء الشيتات في الملف:");
                foreach (var sheet in wb.Worksheets)
                {
                    System.Diagnostics.Debug.WriteLine($"   - {sheet.Name}");
                }

                var mainSheet = wb.Worksheets.SingleOrDefault(ws => ws.Name.Trim().Equals("نموذج استمارة الزيارة الميدانية", StringComparison.OrdinalIgnoreCase));
                var projectSheet = wb.Worksheets.SingleOrDefault(ws => ws.Name.Contains("project_repeat", StringComparison.OrdinalIgnoreCase));
                var dayPlanSheet = wb.Worksheets.SingleOrDefault(ws => ws.Name.Contains("day_plan_repeat", StringComparison.OrdinalIgnoreCase));

                System.Diagnostics.Debug.WriteLine($"🔍 الشيت الرئيسي: {(mainSheet != null ? "موجود" : "مفقود")}");
                System.Diagnostics.Debug.WriteLine($"🔍 شيت المشاريع: {(projectSheet != null ? "موجود" : "مفقود")}");
                System.Diagnostics.Debug.WriteLine($"🔍 شيت خط السير: {(dayPlanSheet != null ? "موجود" : "مفقود")}");

                if (mainSheet == null || projectSheet == null || dayPlanSheet == null)
                {
                    result.ErrorMessage = "الملف يفتقد إحدى الشيتات المطلوبة.";
                    return result;
                }

                // ===== شيت الزيارة =====
                var mainTable = mainSheet.RangeUsed().AsTable();
                var visitRow   = mainTable.DataRange.Rows()
                                   .FirstOrDefault(r => r.Field("_index").GetString().Trim() == indexValue);

                if (visitRow == null)
                {
                    result.ErrorMessage = $"لم يتم العثور على الرقم المرجعي {indexValue}";
                    return result;
                }

                // قراءة أسماء القائمين بالزيارة مباشرة من Excel
                var visitorsText = visitRow.Field("visitors").GetString().Trim();
                var visitorsNames = visitorsText; // عرض النص كما هو من Excel

                result.VisitData = new VisitImportData
                {
                    VisitFormNumber = visitRow.Field("visit_form_number").GetString().Trim(),
                    FieldDaysCount  = visitRow.Field("field_days_count").GetValue<int>(),
                    StartDate       = visitRow.Field("start_date").GetDateTime(),
                    EndDate         = visitRow.Field("end_date").GetDateTime(),
                    Sector          = visitRow.Field("sector").GetString().Trim(),
                    Visitors        = visitorsNames, // الأسماء من Excel مباشرة
                    VisitorCodes    = visitorsText, // النص الأصلي للمرجع
                    TripPurpose     = visitRow.Field("trip_purpose").GetString().Trim(),
                    SecurityRoute   = visitRow.Field("security_route").GetString().Trim(),
                    VisitNotes      = visitRow.Field("visit_notes").GetString().Trim(),

                    // الحقول الجديدة (اختيارية)
                    ApprovalBy      = TryGetFieldValue(visitRow, "approval_by"),
                    SubmissionTime  = TryGetFieldDateTime(visitRow, "_submission_time"),
                    OdkVisitNumber  = TryGetFieldValue(visitRow, "odk_visit_number")
                };

                // تشخيص إضافي للحقول الجديدة
                System.Diagnostics.Debug.WriteLine($"🔍 الحقول الجديدة من Excel:");
                System.Diagnostics.Debug.WriteLine($"   - approval_by: '{result.VisitData.ApprovalBy}'");
                System.Diagnostics.Debug.WriteLine($"   - _submission_time: '{result.VisitData.SubmissionTime}'");
                System.Diagnostics.Debug.WriteLine($"   - odk_visit_number: '{result.VisitData.OdkVisitNumber}'");
                System.Diagnostics.Debug.WriteLine($"   - visit_form_number: '{result.VisitData.VisitFormNumber}'");

                // ===== شيت المشاريع =====
                var projTable = projectSheet.RangeUsed().AsTable();
                foreach (var row in projTable.DataRange.Rows()
                                             .Where(r => r.Field("_parent_index").GetString().Trim() == indexValue)
                                             .Take(MAX_ITEMS))
                {
                    result.Projects.Add(new ProjectImportData
                    {
                        ProjectCode = row.Field("project_code").GetString().Trim(),
                        ProjectName = row.Field("project_name").GetString().Trim(),
                        ProjectDays = row.Field("project_days").GetValue<int>()
                    });
                }

                // ===== شيت خط السير =====
                var dayTable = dayPlanSheet.RangeUsed().AsTable();
                System.Diagnostics.Debug.WriteLine($"🔍 شيت خط السير: {dayPlanSheet.Name}");
                System.Diagnostics.Debug.WriteLine($"🔍 عدد الصفوف في شيت خط السير: {dayTable.DataRange.RowCount()}");

                var dayRows = dayTable.DataRange.Rows()
                                      .Where(r => r.Field("_parent_index").GetString().Trim() == indexValue)
                                      .OrderBy(r => r.Field("day_num").GetValue<int>())
                                      .Take(MAX_ITEMS)
                                      .ToList();

                System.Diagnostics.Debug.WriteLine($"🔍 عدد صفوف خط السير للرقم المرجعي {indexValue}: {dayRows.Count}");

                foreach (var row in dayRows)
                {
                    try
                    {
                        var dayNum = row.Field("day_num").GetValue<int>();
                        var plan = row.Field("day_plan").GetString();
                        System.Diagnostics.Debug.WriteLine($"🔍 اليوم {dayNum}: {plan}");

                        if (!string.IsNullOrWhiteSpace(plan))
                        {
                            result.Itinerary.Add(new ItineraryDayImportData
                            {
                                DayNumber = dayNum,
                                Plan = plan.Trim()
                            });
                            System.Diagnostics.Debug.WriteLine($"✅ تم إضافة اليوم {dayNum}");
                        }
                        else
                        {
                            System.Diagnostics.Debug.WriteLine($"⚠️ خط السير فارغ لليوم {dayNum}");
                        }
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"❌ خطأ في قراءة صف خط السير: {ex.Message}");
                    }
                }

                System.Diagnostics.Debug.WriteLine($"🎯 إجمالي أيام خط السير المستوردة: {result.Itinerary.Count}");

                // التحقق من صحة البيانات المستوردة
                var validationService = new ExcelValidationService(_dataService);
                var validationResult = await validationService.ValidateImportDataAsync(result);

                result.ValidationResult = validationResult;
                result.Success = validationResult.IsValid;

                if (!validationResult.IsValid)
                {
                    result.ErrorMessage = $"البيانات المستوردة تحتوي على أخطاء:\n{string.Join("\n", validationResult.Errors)}";
                    if (validationResult.HasWarnings)
                    {
                        result.ErrorMessage += $"\n\nتحذيرات:\n{string.Join("\n", validationResult.Warnings)}";
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"استثناء: {ex.Message}";
                return result;
            }
        }



        /// <summary>
        /// محاولة الحصول على قيمة نصية من حقل (مع معالجة الحقول المفقودة)
        /// </summary>
        private string TryGetFieldValue(IXLRangeRow row, string fieldName)
        {
            try
            {
                var cell = row.Field(fieldName);
                return cell?.GetString()?.Trim() ?? string.Empty;
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ الحقل '{fieldName}' غير موجود في ملف Excel");
                return string.Empty;
            }
        }

        /// <summary>
        /// محاولة الحصول على قيمة تاريخ من حقل (مع معالجة الحقول المفقودة)
        /// </summary>
        private DateTime? TryGetFieldDateTime(IXLRangeRow row, string fieldName)
        {
            try
            {
                var cell = row.Field(fieldName);
                return cell?.GetDateTime();
            }
            catch
            {
                System.Diagnostics.Debug.WriteLine($"⚠️ الحقل '{fieldName}' غير موجود في ملف Excel");
                return null;
            }
        }
    }

    #region DTOs
    public class FieldVisitImportResult
    {
        public bool Success { get; set; }
        public string ErrorMessage { get; set; } = string.Empty;
        public VisitImportData? VisitData { get; set; }
        public List<ProjectImportData> Projects { get; set; } = new();
        public List<ItineraryDayImportData> Itinerary { get; set; } = new();
        public ValidationResult? ValidationResult { get; set; }
    }

    public class VisitImportData
    {
        public string VisitFormNumber { get; set; } = string.Empty;
        public int FieldDaysCount { get; set; }
        public DateTime? StartDate { get; set; }
        public DateTime? EndDate { get; set; }
        public string Sector { get; set; } = string.Empty;
        public string Visitors { get; set; } = string.Empty; // أسماء القائمين بالزيارة
        public string VisitorCodes { get; set; } = string.Empty; // أكواد القائمين بالزيارة الأصلية
        public string TripPurpose { get; set; } = string.Empty;
        public string SecurityRoute { get; set; } = string.Empty;
        public string VisitNotes { get; set; } = string.Empty;

        // الحقول الجديدة
        public string ApprovalBy { get; set; } = string.Empty; // الموافقة على السفر
        public DateTime? SubmissionTime { get; set; } // وقت وتاريخ الإرسال
        public string OdkVisitNumber { get; set; } = string.Empty; // رقم الزيارة ODK
    }

    public class ProjectImportData
    {
        public string ProjectCode { get; set; } = string.Empty;
        public string ProjectName { get; set; } = string.Empty;
        public int ProjectDays { get; set; }
    }

    public class ItineraryDayImportData
    {
        public int DayNumber { get; set; }
        public string Plan { get; set; } = string.Empty;
    }
    #endregion
}
