using System;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using Prism.Commands;
using Prism.Mvvm;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.ViewModels
{
    public class VehicleManagementViewModel : BindableBase
    {
        private readonly SqliteDataService _dataService;
        private Driver? _selectedDriver;
        private string _driverName = string.Empty;
        private string _cardNumber = string.Empty;
        private string _cardIssuePlace = string.Empty;
        private DateTime _cardIssueDate = DateTime.Now;
        private string _licenseNumber = string.Empty;
        private DateTime _licenseIssueDate = DateTime.Now;
        private string _vehicleNumber = string.Empty;
        private string _selectedVehicleType = string.Empty;
        private string _selectedVehicleColor = string.Empty;
        private string _vehicleModel = string.Empty;
        private string _selectedCardType = string.Empty;
        private string _phoneNumber = string.Empty;
        private string _selectedVehicleCapacity = string.Empty;
        private string _driverCode = string.Empty;
        private string _notes = string.Empty;

        // Filter properties
        private string _searchText = string.Empty;
        private string _selectedVehicleTypeFilter = string.Empty;
        private string _selectedVehicleCapacityFilter = string.Empty;
        private ObservableCollection<Driver> _filteredDrivers = new ObservableCollection<Driver>();
        private int _totalDriversCount;
        private int _filteredDriversCount;

        public VehicleManagementViewModel()
        {
            _dataService = new SqliteDataService();
            
            // Initialize collections
            Drivers = new ObservableCollection<Driver>();
            FilteredDrivers = new ObservableCollection<Driver>();

            // Initialize dropdown data
            CardTypes = new ObservableCollection<string>
            {
                "بطاقة شخصية",
                "جواز سفر",
                "بطاقة عائلية",
                "بطاقة عسكرية"
            };

            VehicleTypes = new ObservableCollection<string>
            {
                "فورشنال",
                "هيلوكس",
                "برادو",
                "كنتر",
                "حافلة"
            };

            VehicleColors = new ObservableCollection<string>
            {
                "أبيض",
                "أسود",
                "فضي",
                "رمادي",
                "أزرق",
                "أحمر",
                "بني",
                "ذهبي"
            };

            VehicleCapacities = new ObservableCollection<string>
            {
                "4 بسطون",
                "6 بسطون"
            };

            // Initialize filter collections
            VehicleTypesFilter = new ObservableCollection<string>
            {
                "الكل",
                "فورشنال",
                "هيلوكس",
                "برادو",
                "كنتر",
                "حافلة"
            };

            VehicleCapacitiesFilter = new ObservableCollection<string>
            {
                "الكل",
                "4 بسطون",
                "6 بسطون"
            };

            // Set default filter values
            SelectedVehicleTypeFilter = "الكل";
            SelectedVehicleCapacityFilter = "الكل";

            // Initialize commands
            AddCommand = new DelegateCommand(AddDriver, CanExecuteAdd);
            SaveCommand = new DelegateCommand(SaveDriver, CanExecuteSave);
            DeleteCommand = new DelegateCommand(DeleteDriver, CanExecuteDelete);
            PreviousCommand = new DelegateCommand(PreviousRecord);
            NextCommand = new DelegateCommand(NextRecord);
            ApplyFilterCommand = new DelegateCommand(ApplyFilter);
            ClearFilterCommand = new DelegateCommand(ClearFilter);

            // Load initial data
            LoadData();
        }

        #region Properties

        public ObservableCollection<Driver> Drivers { get; }
        public ObservableCollection<string> CardTypes { get; }
        public ObservableCollection<string> VehicleTypes { get; }
        public ObservableCollection<string> VehicleColors { get; }
        public ObservableCollection<string> VehicleCapacities { get; }

        // Filter collections
        public ObservableCollection<string> VehicleTypesFilter { get; }
        public ObservableCollection<string> VehicleCapacitiesFilter { get; }

        public ObservableCollection<Driver> FilteredDrivers
        {
            get => _filteredDrivers;
            set => SetProperty(ref _filteredDrivers, value);
        }

        public int TotalDriversCount
        {
            get => _totalDriversCount;
            set => SetProperty(ref _totalDriversCount, value);
        }

        public int FilteredDriversCount
        {
            get => _filteredDriversCount;
            set => SetProperty(ref _filteredDriversCount, value);
        }

        public string SearchText
        {
            get => _searchText;
            set
            {
                SetProperty(ref _searchText, value);
                ApplyFilter(); // Auto-filter on text change
            }
        }

        public string SelectedVehicleTypeFilter
        {
            get => _selectedVehicleTypeFilter;
            set
            {
                SetProperty(ref _selectedVehicleTypeFilter, value);
                ApplyFilter(); // Auto-filter on selection change
            }
        }

        public string SelectedVehicleCapacityFilter
        {
            get => _selectedVehicleCapacityFilter;
            set
            {
                SetProperty(ref _selectedVehicleCapacityFilter, value);
                ApplyFilter(); // Auto-filter on selection change
            }
        }

        public Driver? SelectedDriver
        {
            get => _selectedDriver;
            set
            {
                SetProperty(ref _selectedDriver, value);
                LoadDriverDetails();
                RaiseCanExecuteChanged();
            }
        }

        public string DriverName
        {
            get => _driverName;
            set
            {
                SetProperty(ref _driverName, value);
                RaiseCanExecuteChanged();
            }
        }

        public string CardNumber
        {
            get => _cardNumber;
            set => SetProperty(ref _cardNumber, value);
        }

        public string CardIssuePlace
        {
            get => _cardIssuePlace;
            set => SetProperty(ref _cardIssuePlace, value);
        }

        public DateTime CardIssueDate
        {
            get => _cardIssueDate;
            set => SetProperty(ref _cardIssueDate, value);
        }

        public string LicenseNumber
        {
            get => _licenseNumber;
            set => SetProperty(ref _licenseNumber, value);
        }

        public DateTime LicenseIssueDate
        {
            get => _licenseIssueDate;
            set => SetProperty(ref _licenseIssueDate, value);
        }

        public string VehicleNumber
        {
            get => _vehicleNumber;
            set => SetProperty(ref _vehicleNumber, value);
        }

        public string SelectedVehicleType
        {
            get => _selectedVehicleType;
            set => SetProperty(ref _selectedVehicleType, value);
        }

        public string SelectedVehicleColor
        {
            get => _selectedVehicleColor;
            set => SetProperty(ref _selectedVehicleColor, value);
        }

        public string VehicleModel
        {
            get => _vehicleModel;
            set => SetProperty(ref _vehicleModel, value);
        }

        public string SelectedCardType
        {
            get => _selectedCardType;
            set => SetProperty(ref _selectedCardType, value);
        }

        public string PhoneNumber
        {
            get => _phoneNumber;
            set => SetProperty(ref _phoneNumber, value);
        }

        public string SelectedVehicleCapacity
        {
            get => _selectedVehicleCapacity;
            set => SetProperty(ref _selectedVehicleCapacity, value);
        }

        public string DriverCode
        {
            get => _driverCode;
            set => SetProperty(ref _driverCode, value);
        }

        public string Notes
        {
            get => _notes;
            set => SetProperty(ref _notes, value);
        }

        #endregion

        #region Commands

        public DelegateCommand AddCommand { get; }
        public DelegateCommand SaveCommand { get; }
        public DelegateCommand DeleteCommand { get; }
        public DelegateCommand PreviousCommand { get; }
        public DelegateCommand NextCommand { get; }
        public DelegateCommand ApplyFilterCommand { get; }
        public DelegateCommand ClearFilterCommand { get; }

        #endregion

        #region Command Methods

        private async void AddDriver()
        {
            try
            {
                // توليد كود السائق آلياً
                var driverCode = await GenerateDriverCodeAsync();

                var newDriver = new Driver
                {
                    DriverCode = driverCode, // كود السائق الآلي

                    // بيانات السائق
                    Name = DriverName,
                    PhoneNumber = PhoneNumber,
                    CardNumber = CardNumber,
                    CardType = SelectedCardType,
                    CardIssuePlace = CardIssuePlace,
                    CardIssueDate = CardIssueDate,

                    // بيانات السيارة
                    VehicleType = SelectedVehicleType,
                    VehicleNumber = VehicleNumber,
                    VehicleModel = VehicleModel,
                    VehicleCapacity = SelectedVehicleCapacity,
                    LicenseNumber = LicenseNumber,
                    LicenseIssueDate = LicenseIssueDate,

                    // حقول إضافية
                    VehicleColor = SelectedVehicleColor,
                    Notes = Notes
                };

                // حفظ في قاعدة البيانات
                var success = await _dataService.AddDriverAsync(newDriver);

                if (success)
                {
                    Drivers.Add(newDriver);
                    TotalDriversCount = Drivers.Count;
                    ApplyFilter(); // Update filtered list
                    ClearForm();
                    MessageBox.Show($"تم إضافة السائق والسيارة بنجاح\nكود السائق: {driverCode}", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في إضافة السائق والسيارة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إضافة السائق والسيارة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void SaveDriver()
        {
            try
            {
                if (SelectedDriver == null) return;

                // تحديث بيانات السائق
                SelectedDriver.Name = DriverName;
                SelectedDriver.PhoneNumber = PhoneNumber;
                SelectedDriver.CardNumber = CardNumber;
                SelectedDriver.CardType = SelectedCardType;
                SelectedDriver.CardIssuePlace = CardIssuePlace;
                SelectedDriver.CardIssueDate = CardIssueDate;

                // تحديث بيانات السيارة
                SelectedDriver.VehicleType = SelectedVehicleType;
                SelectedDriver.VehicleNumber = VehicleNumber;
                SelectedDriver.VehicleModel = VehicleModel;
                SelectedDriver.VehicleCapacity = SelectedVehicleCapacity;
                SelectedDriver.LicenseNumber = LicenseNumber;
                SelectedDriver.LicenseIssueDate = LicenseIssueDate;

                // تحديث الحقول الإضافية
                SelectedDriver.VehicleColor = SelectedVehicleColor;
                SelectedDriver.Notes = Notes;

                // ملاحظة: كود السائق لا يتغير بعد الإنشاء

                // تحديث في قاعدة البيانات
                var success = await _dataService.UpdateDriverAsync(SelectedDriver);

                if (success)
                {
                    MessageBox.Show("تم حفظ التعديلات بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                else
                {
                    MessageBox.Show("فشل في حفظ التعديلات", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حفظ التعديلات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private async void DeleteDriver()
        {
            try
            {
                if (SelectedDriver == null) return;

                var result = MessageBox.Show(
                    $"هل أنت متأكد من حذف السائق: {SelectedDriver.Name}؟",
                    "تأكيد الحذف",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // حذف من قاعدة البيانات
                    var success = await _dataService.DeleteDriverAsync(SelectedDriver.Id);
                    
                    if (success)
                    {
                        Drivers.Remove(SelectedDriver);
                        TotalDriversCount = Drivers.Count;
                        ApplyFilter(); // Update filtered list
                        ClearForm();
                        MessageBox.Show("تم حذف السائق والسيارة بنجاح", "نجح", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("فشل في حذف السائق والسيارة", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في حذف السائق والسيارة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void PreviousRecord()
        {
            if (FilteredDrivers.Count == 0) return;

            var currentIndex = SelectedDriver != null ? FilteredDrivers.IndexOf(SelectedDriver) : -1;
            var previousIndex = currentIndex > 0 ? currentIndex - 1 : FilteredDrivers.Count - 1;
            SelectedDriver = FilteredDrivers[previousIndex];
        }

        private void NextRecord()
        {
            if (FilteredDrivers.Count == 0) return;

            var currentIndex = SelectedDriver != null ? FilteredDrivers.IndexOf(SelectedDriver) : -1;
            var nextIndex = currentIndex < FilteredDrivers.Count - 1 ? currentIndex + 1 : 0;
            SelectedDriver = FilteredDrivers[nextIndex];
        }

        private void ApplyFilter()
        {
            try
            {
                var filtered = Drivers.AsEnumerable();

                // Filter by search text
                if (!string.IsNullOrWhiteSpace(SearchText))
                {
                    filtered = filtered.Where(d =>
                        d.Name.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        d.CardNumber.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        d.PhoneNumber.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        d.VehicleNumber.Contains(SearchText, StringComparison.OrdinalIgnoreCase) ||
                        d.DriverCode.Contains(SearchText, StringComparison.OrdinalIgnoreCase));
                }

                // Filter by vehicle type
                if (!string.IsNullOrWhiteSpace(SelectedVehicleTypeFilter) && SelectedVehicleTypeFilter != "الكل")
                {
                    filtered = filtered.Where(d => d.VehicleType == SelectedVehicleTypeFilter);
                }

                // Filter by vehicle capacity
                if (!string.IsNullOrWhiteSpace(SelectedVehicleCapacityFilter) && SelectedVehicleCapacityFilter != "الكل")
                {
                    filtered = filtered.Where(d => d.VehicleCapacity == SelectedVehicleCapacityFilter);
                }

                // Update filtered collection
                FilteredDrivers.Clear();
                foreach (var driver in filtered.OrderBy(d => d.Name))
                {
                    FilteredDrivers.Add(driver);
                }

                // Update counts
                FilteredDriversCount = FilteredDrivers.Count;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في الفلترة: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ClearFilter()
        {
            SearchText = string.Empty;
            SelectedVehicleTypeFilter = "الكل";
            SelectedVehicleCapacityFilter = "الكل";
            ApplyFilter();
        }

        #endregion

        #region Can Execute Methods

        private bool CanExecuteAdd()
        {
            return !string.IsNullOrWhiteSpace(DriverName);
        }

        private bool CanExecuteSave()
        {
            return SelectedDriver != null && !string.IsNullOrWhiteSpace(DriverName);
        }

        private bool CanExecuteDelete()
        {
            return SelectedDriver != null;
        }

        #endregion

        #region Helper Methods

        public async void LoadData()
        {
            try
            {
                var drivers = await _dataService.GetDriversAsync();
                Drivers.Clear();
                foreach (var driver in drivers)
                {
                    Drivers.Add(driver);
                }

                // Update statistics
                TotalDriversCount = Drivers.Count;

                // Apply initial filter to show all data
                ApplyFilter();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل البيانات: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void LoadDriverDetails()
        {
            if (SelectedDriver == null)
            {
                ClearForm();
                return;
            }

            // تحميل بيانات السائق
            DriverName = SelectedDriver.Name;
            PhoneNumber = SelectedDriver.PhoneNumber;
            CardNumber = SelectedDriver.CardNumber;
            SelectedCardType = SelectedDriver.CardType;
            CardIssuePlace = SelectedDriver.CardIssuePlace;
            CardIssueDate = SelectedDriver.CardIssueDate;

            // تحميل بيانات السيارة
            SelectedVehicleType = SelectedDriver.VehicleType;
            VehicleNumber = SelectedDriver.VehicleNumber;
            VehicleModel = SelectedDriver.VehicleModel;
            SelectedVehicleCapacity = SelectedDriver.VehicleCapacity;
            LicenseNumber = SelectedDriver.LicenseNumber;
            LicenseIssueDate = SelectedDriver.LicenseIssueDate;

            // تحميل الحقول الإضافية
            SelectedVehicleColor = SelectedDriver.VehicleColor;
            Notes = SelectedDriver.Notes;

            // تحميل كود السائق (للعرض فقط)
            DriverCode = SelectedDriver.DriverCode;
        }

        private void ClearForm()
        {
            // مسح بيانات السائق
            DriverName = string.Empty;
            PhoneNumber = string.Empty;
            CardNumber = string.Empty;
            SelectedCardType = string.Empty;
            CardIssuePlace = string.Empty;
            CardIssueDate = DateTime.Now;

            // مسح بيانات السيارة
            SelectedVehicleType = string.Empty;
            VehicleNumber = string.Empty;
            VehicleModel = string.Empty;
            SelectedVehicleCapacity = string.Empty;
            LicenseNumber = string.Empty;
            LicenseIssueDate = DateTime.Now;

            // مسح الحقول الإضافية
            SelectedVehicleColor = string.Empty;
            Notes = string.Empty;

            // مسح كود السائق (سيتم توليده آلياً عند الإضافة)
            DriverCode = string.Empty;
        }

        private void RaiseCanExecuteChanged()
        {
            AddCommand.RaiseCanExecuteChanged();
            SaveCommand.RaiseCanExecuteChanged();
            DeleteCommand.RaiseCanExecuteChanged();
        }

        private async Task<string> GenerateDriverCodeAsync()
        {
            try
            {
                // الحصول على جميع السائقين الحاليين
                var allDrivers = await _dataService.GetDriversAsync();

                // البحث عن أعلى كود موجود
                int maxCode = 0;
                foreach (var driver in allDrivers)
                {
                    if (!string.IsNullOrEmpty(driver.DriverCode))
                    {
                        // استخراج الرقم من الكود (مثل 911102 -> 102)
                        if (driver.DriverCode.Length >= 3)
                        {
                            var numberPart = driver.DriverCode.Substring(3); // إزالة "911"
                            if (int.TryParse(numberPart, out int code))
                            {
                                maxCode = Math.Max(maxCode, code);
                            }
                        }
                    }
                }

                // توليد الكود الجديد
                var newCodeNumber = maxCode + 1;
                var newDriverCode = $"911{newCodeNumber:D3}"; // تنسيق مثل 911103

                return newDriverCode;
            }
            catch (Exception)
            {
                // في حالة الخطأ، استخدم timestamp
                var timestamp = DateTime.Now.ToString("HHmmss");
                return $"911{timestamp.Substring(0, 3)}";
            }
        }

        #endregion
    }
}
