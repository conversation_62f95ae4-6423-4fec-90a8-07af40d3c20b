using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Microsoft.EntityFrameworkCore;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Migrations
{
    /// <summary>
    /// تحديث أكواد القطاعات لتتطابق مع ملف Excel
    /// </summary>
    public class UpdateSectorCodes
    {
        private readonly ApplicationDbContext _context;

        public UpdateSectorCodes(ApplicationDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// تطبيق التحديث - إضافة أكواد القطاعات
        /// </summary>
        public async Task ApplyAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء تحديث أكواد القطاعات...");

                // قائمة القطاعات مع الأكواد الجديدة
                var sectorUpdates = new Dictionary<string, string>
                {
                    { "القطاع", "QTAA" },
                    { "التدريب", "training" },
                    { "التعاقدات", "contracts" },
                    { "التعليم", "education" },
                    { "التمكين", "empowerment" },
                    { "الحسابات", "accounts" },
                    { "الزراعة", "agriculture" },
                    { "الشكاوى والامتثال", "complaints_compliance" },
                    { "الصحة والحماية الاجتماعية", "health_social_protection" },
                    { "الطرق", "roads" },
                    { "الفنية", "technical" },
                    { "المراقبة والتقييم", "monitoring_evaluation" },
                    { "المياه والبيئة", "water_environment" },
                    { "النقد مقابل العمل", "cash_for_work" }
                };

                // تحديث القطاعات الموجودة
                var existingSectors = await _context.Sectors.ToListAsync();
                int updatedCount = 0;

                foreach (var sector in existingSectors)
                {
                    if (sectorUpdates.ContainsKey(sector.Name))
                    {
                        sector.Code = sectorUpdates[sector.Name];
                        _context.Sectors.Update(sector);
                        updatedCount++;
                        System.Diagnostics.Debug.WriteLine($"✅ تم تحديث القطاع: {sector.Name} -> {sector.Code}");
                    }
                }

                // إضافة القطاعات المفقودة
                var existingNames = existingSectors.Select(s => s.Name).ToHashSet();
                var missingUpdates = sectorUpdates.Where(kvp => !existingNames.Contains(kvp.Key));

                foreach (var missing in missingUpdates)
                {
                    var newSector = new Sector
                    {
                        Code = missing.Value,
                        Name = missing.Key,
                        Description = $"قطاع {missing.Key}",
                        IsActive = true,
                        CreatedAt = DateTime.Now
                    };

                    _context.Sectors.Add(newSector);
                    updatedCount++;
                    System.Diagnostics.Debug.WriteLine($"➕ تم إضافة القطاع الجديد: {newSector.Name} -> {newSector.Code}");
                }

                // حفظ التغييرات
                await _context.SaveChangesAsync();

                System.Diagnostics.Debug.WriteLine($"✅ تم تحديث {updatedCount} قطاع بنجاح");
                System.Diagnostics.Debug.WriteLine("🎉 تحديث أكواد القطاعات مكتمل!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في تحديث أكواد القطاعات: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// التراجع عن التحديث - إزالة أكواد القطاعات
        /// </summary>
        public async Task RevertAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🔄 بدء التراجع عن تحديث أكواد القطاعات...");

                var sectors = await _context.Sectors.ToListAsync();
                foreach (var sector in sectors)
                {
                    sector.Code = string.Empty;
                    _context.Sectors.Update(sector);
                }

                await _context.SaveChangesAsync();
                System.Diagnostics.Debug.WriteLine("✅ تم التراجع عن تحديث أكواد القطاعات بنجاح");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ خطأ في التراجع عن تحديث أكواد القطاعات: {ex.Message}");
                throw;
            }
        }
    }
}
