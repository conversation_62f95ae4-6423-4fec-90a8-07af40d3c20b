using System;
using System.Threading.Tasks;
using System.Windows;
using System.IO;
using System.Diagnostics;

namespace SFDSystem.Services
{
    /// <summary>
    /// خدمة الإرسال التلقائي للواتس اب باستخدام WhatsApp Desktop فقط
    /// </summary>
    public class WhatsAppAutomationService
    {
        /// <summary>
        /// إرسال رسالة تلقائياً عبر WhatsApp Desktop
        /// </summary>
        public async Task<bool> SendMessageAutomaticallyAsync(string phoneNumber, string message)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine($"🖥️ Starting WhatsApp Desktop sending to {phoneNumber}");

                // تنظيف رقم الهاتف
                string cleanPhoneNumber = CleanPhoneNumber(phoneNumber);
                if (string.IsNullOrEmpty(cleanPhoneNumber))
                {
                    throw new ArgumentException("رقم الهاتف غير صحيح");
                }

                // إرسال عبر WhatsApp Desktop
                return await SendViaWhatsAppDesktop(cleanPhoneNumber, message);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error in WhatsApp Desktop sending: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إرسال الرسالة عبر WhatsApp Desktop
        /// </summary>
        private async Task<bool> SendViaWhatsAppDesktop(string phoneNumber, string message)
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("🖥️ Using WhatsApp Desktop...");

                // إنشاء رابط WhatsApp Desktop
                string whatsappDesktopUrl = $"whatsapp://send?phone={phoneNumber}&text={Uri.EscapeDataString(message)}";
                System.Diagnostics.Debug.WriteLine($"🔗 Opening: {whatsappDesktopUrl}");

                // فتح WhatsApp Desktop
                Process.Start(new ProcessStartInfo
                {
                    FileName = whatsappDesktopUrl,
                    UseShellExecute = true
                });

                // انتظار قصير لفتح التطبيق
                await Task.Delay(1500);

                // محاولة إرسال الرسالة بضغط Enter
                return await SendMessageWithEnter();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error with WhatsApp Desktop: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// إرسال الرسالة بضغط Enter باستخدام Win32 API
        /// </summary>
        private async Task<bool> SendMessageWithEnter()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("⌨️ Sending message with Enter key...");

                // انتظار قصير لتحميل WhatsApp Desktop
                await Task.Delay(2000);

                // محاولة ضغط Enter لإرسال الرسالة باستخدام Win32 API
                const int VK_RETURN = 0x0D;
                const int KEYEVENTF_KEYDOWN = 0x0000;
                const int KEYEVENTF_KEYUP = 0x0002;

                // ضغط Enter
                keybd_event(VK_RETURN, 0, KEYEVENTF_KEYDOWN, 0);
                await Task.Delay(50);
                keybd_event(VK_RETURN, 0, KEYEVENTF_KEYUP, 0);

                System.Diagnostics.Debug.WriteLine("✅ Enter key sent!");

                // انتظار قصير للتأكد من الإرسال
                await Task.Delay(500);

                return true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error sending Enter: {ex.Message}");
                return false;
            }
        }

        // Win32 API للوحة المفاتيح
        [System.Runtime.InteropServices.DllImport("user32.dll")]
        private static extern void keybd_event(byte bVk, byte bScan, uint dwFlags, uint dwExtraInfo);

        /// <summary>
        /// تنظيف رقم الهاتف
        /// </summary>
        private string CleanPhoneNumber(string phoneNumber)
        {
            if (string.IsNullOrWhiteSpace(phoneNumber))
                return string.Empty;

            // إزالة المسافات والرموز غير المرغوبة
            string cleaned = phoneNumber.Replace(" ", "")
                                      .Replace("-", "")
                                      .Replace("(", "")
                                      .Replace(")", "")
                                      .Replace("+", "");

            // إضافة رمز السعودية إذا لم يكن موجوداً
            if (!cleaned.StartsWith("966") && cleaned.StartsWith("05"))
            {
                cleaned = "966" + cleaned.Substring(1);
            }
            else if (!cleaned.StartsWith("966") && cleaned.StartsWith("5"))
            {
                cleaned = "966" + cleaned;
            }

            return cleaned;
        }
    }
}
