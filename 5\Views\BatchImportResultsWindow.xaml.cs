using System;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Text;
using System.Windows;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    /// <summary>
    /// نافذة عرض نتائج الاستيراد المتعدد
    /// </summary>
    public partial class BatchImportResultsWindow : Window, INotifyPropertyChanged
    {
        public BatchImportResult ImportResult { get; private set; }

        public BatchImportResultsWindow(BatchImportResult importResult)
        {
            InitializeComponent();
            ImportResult = importResult;
            DataContext = this;
            
            System.Diagnostics.Debug.WriteLine($"📊 تم فتح نافذة نتائج الاستيراد - نجح: {importResult.SuccessfulFiles}, فشل: {importResult.FailedFiles}");
        }

        #region Properties for Binding

        public int TotalFiles => ImportResult.TotalFiles;
        public int SuccessfulFiles => ImportResult.SuccessfulFiles;
        public int FailedFiles => ImportResult.FailedFiles;
        public double SuccessRate => ImportResult.SuccessRate;
        public string ProcessingTimeText => $"{ImportResult.ProcessingTime.TotalSeconds:F1}s";
        public System.Collections.Generic.List<SingleFileImportResult> FileResults => ImportResult.FileResults;

        #endregion

        private void ExportResultsButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "تصدير نتائج الاستيراد",
                    Filter = "ملفات CSV (*.csv)|*.csv|ملفات النص (*.txt)|*.txt|جميع الملفات (*.*)|*.*",
                    FileName = $"BatchImportResults_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveDialog.ShowDialog() == true)
                {
                    ExportResultsToFile(saveDialog.FileName);
                    MessageBox.Show("✅ تم تصدير النتائج بنجاح!", "تصدير", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تصدير النتائج:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void RetryFailedButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var failedFiles = ImportResult.FileResults.Where(f => !f.Success).ToList();
                
                if (!failedFiles.Any())
                {
                    MessageBox.Show("لا توجد ملفات فاشلة لإعادة المحاولة", "معلومات", MessageBoxButton.OK, MessageBoxImage.Information);
                    return;
                }

                var result = MessageBox.Show($"هل تريد إعادة المحاولة لـ {failedFiles.Count} ملف فاشل؟", 
                                           "إعادة المحاولة", MessageBoxButton.YesNo, MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    var retryWindow = new BatchImportWindow();
                    
                    // إضافة الملفات الفاشلة إلى النافذة الجديدة
                    foreach (var failedFile in failedFiles)
                    {
                        var fileInfo = new ExcelFileInfo
                        {
                            FileName = failedFile.FileName,
                            FilePath = failedFile.FilePath,
                            FileSize = "غير معروف",
                            LastModified = DateTime.Now
                        };
                        retryWindow.SelectedFiles.Add(fileInfo);
                    }

                    retryWindow.ShowDialog();
                    Close();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة المحاولة:\n{ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void CloseButton_Click(object sender, RoutedEventArgs e)
        {
            Close();
        }

        private void ExportResultsToFile(string filePath)
        {
            var csv = new StringBuilder();
            
            // Header
            csv.AppendLine("اسم الملف,الرقم المرجعي,رقم الزيارة,المشاريع,خط السير,الحالة,وقت المعالجة,رسالة الخطأ");
            
            // Data
            foreach (var result in ImportResult.FileResults)
            {
                var status = result.Success ? "نجح" : "فشل";
                var errorMessage = result.ErrorMessage.Replace(",", ";").Replace("\n", " ");
                
                csv.AppendLine($"{result.FileName},{result.IndexValue},{result.VisitNumber}," +
                             $"{result.ProjectsCount},{result.ItineraryDaysCount},{status}," +
                             $"{result.ProcessingTime.TotalSeconds:F1},{errorMessage}");
            }
            
            // Summary
            csv.AppendLine();
            csv.AppendLine("ملخص النتائج:");
            csv.AppendLine($"إجمالي الملفات,{ImportResult.TotalFiles}");
            csv.AppendLine($"نجح,{ImportResult.SuccessfulFiles}");
            csv.AppendLine($"فشل,{ImportResult.FailedFiles}");
            csv.AppendLine($"معدل النجاح,{ImportResult.SuccessRate:F1}%");
            csv.AppendLine($"وقت المعالجة,{ImportResult.ProcessingTime.TotalSeconds:F1} ثانية");
            
            File.WriteAllText(filePath, csv.ToString(), Encoding.UTF8);
        }

        public event PropertyChangedEventHandler? PropertyChanged;
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
