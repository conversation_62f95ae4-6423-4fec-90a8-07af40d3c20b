using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DriverManagementSystem.Models;

namespace DriverManagementSystem.Services
{
    /// <summary>
    /// خدمة الزيارات الميدانية - تطبيق Service Layer
    /// </summary>
    public class FieldVisitService : IFieldVisitService
    {
        private readonly IDataService _dataService;

        public FieldVisitService(IDataService dataService)
        {
            _dataService = dataService;
        }

        /// <summary>
        /// بناء رسالة الزيارة من معرفات الزيارات المحددة
        /// </summary>
        public async Task<string> BuildVisitMessageAsync(IEnumerable<int> fieldVisitIds)
        {
            try
            {
                var visits = await GetSelectedVisitsAsync(fieldVisitIds);
                var sb = new StringBuilder();

                System.Diagnostics.Debug.WriteLine($"🔧 Building message for {visits.Count} visits");

                foreach (var visit in visits)
                {
                    // البحث عن السائق
                    var drivers = await _dataService.GetDriversAsync();
                    var driver = drivers.FirstOrDefault(d => 
                        d.DriverCode.Contains(visit.DriverContract) || 
                        visit.DriverContract.Contains(d.DriverCode));

                    if (driver == null)
                    {
                        System.Diagnostics.Debug.WriteLine($"⚠️ No driver found for contract: {visit.DriverContract}");
                        continue;
                    }

                    // بناء الرسالة
                    var message = await BuildSingleVisitMessage(visit, driver);
                    sb.AppendLine(message);
                    sb.AppendLine(new string('-', 40));
                    sb.AppendLine();
                }

                var result = sb.ToString();
                System.Diagnostics.Debug.WriteLine($"✅ Generated message with {result.Length} characters");
                
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error building visit message: {ex.Message}");
                return "حدث خطأ أثناء إنشاء الرسالة";
            }
        }

        /// <summary>
        /// بناء رسالة لسائق محدد من زيارة محددة
        /// </summary>
        public async Task<string> BuildDriverMessageAsync(int fieldVisitId, string driverName)
        {
            try
            {
                var visits = await _dataService.GetFieldVisitsAsync();
                var visit = visits.FirstOrDefault(v => v.Id == fieldVisitId);
                
                if (visit == null)
                {
                    return $"الأخ/ {driverName}\nلم يتم العثور على بيانات الزيارة";
                }

                var drivers = await _dataService.GetDriversAsync();
                var driver = drivers.FirstOrDefault(d => d.Name == driverName);
                
                if (driver == null)
                {
                    driver = new Driver { Name = driverName };
                }

                return await BuildSingleVisitMessage(visit, driver);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error building driver message: {ex.Message}");
                return $"الأخ/ {driverName}\nحدث خطأ أثناء إنشاء الرسالة";
            }
        }

        /// <summary>
        /// الحصول على جميع الزيارات المحددة
        /// </summary>
        public async Task<List<FieldVisit>> GetSelectedVisitsAsync(IEnumerable<int> fieldVisitIds)
        {
            try
            {
                var allVisits = await _dataService.GetFieldVisitsAsync();
                return allVisits.Where(v => fieldVisitIds.Contains(v.Id)).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error getting selected visits: {ex.Message}");
                return new List<FieldVisit>();
            }
        }

        /// <summary>
        /// بناء رسالة لزيارة واحدة
        /// </summary>
        private Task<string> BuildSingleVisitMessage(FieldVisit visit, Driver driver)
        {
            try
            {
                var sb = new StringBuilder();

                // رأس الرسالة
                sb.AppendLine($"الأخ/ {driver.Name}");
                sb.AppendLine("السلام عليكم ورحمة الله وبركاته،");
                sb.AppendLine();

                // تكوين أسماء الزوار
                var visitorsText = "غير محدد";
                if (visit.Visitors != null && visit.Visitors.Any())
                {
                    var visitorNames = visit.Visitors
                        .Where(v => !string.IsNullOrWhiteSpace(v.OfficerName))
                        .Select(v => v.OfficerName)
                        .ToList();
                    
                    if (visitorNames.Any())
                    {
                        visitorsText = string.Join("، ", visitorNames);
                    }
                }

                // جسم الرسالة
                sb.AppendLine($"يرجى التكرم بتقديم عرض سعركم خلال 24 ساعة للسفر لمدة ({visit.DaysCount} يوم) مع الأخوة: {visitorsText}.");
                sb.AppendLine();

                // خط السير
                sb.AppendLine("في المناطق التالية:");
                if (visit.Itinerary != null && visit.Itinerary.Any())
                {
                    for (int i = 0; i < visit.Itinerary.Count; i++)
                    {
                        if (!string.IsNullOrWhiteSpace(visit.Itinerary[i]))
                        {
                            var dayName = GetArabicDayName(i + 1);
                            sb.AppendLine($"- اليوم {dayName}: {visit.Itinerary[i]}");
                        }
                    }
                }
                else
                {
                    sb.AppendLine("- لم يتم تحديد خط السير");
                }

                sb.AppendLine();
                sb.AppendLine("وتفضلوا بقبول فائق الاحترام،");

                return Task.FromResult(sb.ToString());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"❌ Error building single visit message: {ex.Message}");
                return Task.FromResult($"الأخ/ {driver.Name}\nحدث خطأ أثناء إنشاء الرسالة");
            }
        }

        /// <summary>
        /// الحصول على اسم اليوم بالعربية
        /// </summary>
        private string GetArabicDayName(int dayNumber)
        {
            return dayNumber switch
            {
                1 => "الأول",
                2 => "الثاني",
                3 => "الثالث",
                4 => "الرابع",
                5 => "الخامس",
                6 => "السادس",
                7 => "السابع",
                8 => "الثامن",
                9 => "التاسع",
                10 => "العاشر",
                _ => dayNumber.ToString()
            };
        }
    }
}
