using System;
using System.Text.RegularExpressions;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class EnhancedAddUserWindow : Window
    {
        private readonly IUserService _userService;
        public event EventHandler? UserAdded;

        public EnhancedAddUserWindow(IUserService userService)
        {
            InitializeComponent();
            _userService = userService;
            InitializeForm();
        }

        private void InitializeForm()
        {
            // Set initial validation state
            ValidateForm();
            UpdateRoleDescription();
        }

        private void ValidateForm(object? sender = null, EventArgs? e = null)
        {
            bool isValid = true;
            var errors = new System.Collections.Generic.List<string>();

            // Validate Full Name
            if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
            {
                FullNameError.Visibility = Visibility.Visible;
                FullNameError.Text = "يرجى إدخال الاسم الكامل";
                isValid = false;
                errors.Add("الاسم الكامل");
            }
            else if (FullNameTextBox.Text.Trim().Length < 2)
            {
                FullNameError.Visibility = Visibility.Visible;
                FullNameError.Text = "الاسم يجب أن يكون حرفين على الأقل";
                isValid = false;
            }
            else
            {
                FullNameError.Visibility = Visibility.Collapsed;
            }

            // Validate Username
            if (string.IsNullOrWhiteSpace(UsernameTextBox.Text))
            {
                UsernameError.Visibility = Visibility.Visible;
                UsernameError.Text = "يرجى إدخال اسم المستخدم";
                isValid = false;
                errors.Add("اسم المستخدم");
            }
            else if (UsernameTextBox.Text.Trim().Length < 3)
            {
                UsernameError.Visibility = Visibility.Visible;
                UsernameError.Text = "اسم المستخدم يجب أن يكون 3 أحرف على الأقل";
                isValid = false;
            }
            else if (!IsValidUsername(UsernameTextBox.Text.Trim()))
            {
                UsernameError.Visibility = Visibility.Visible;
                UsernameError.Text = "اسم المستخدم يجب أن يحتوي على أحرف وأرقام فقط";
                isValid = false;
            }
            else
            {
                UsernameError.Visibility = Visibility.Collapsed;
            }

            // Validate Email
            if (string.IsNullOrWhiteSpace(EmailTextBox.Text))
            {
                EmailError.Visibility = Visibility.Visible;
                EmailError.Text = "يرجى إدخال البريد الإلكتروني";
                isValid = false;
                errors.Add("البريد الإلكتروني");
            }
            else if (!IsValidEmail(EmailTextBox.Text.Trim()))
            {
                EmailError.Visibility = Visibility.Visible;
                EmailError.Text = "يرجى إدخال بريد إلكتروني صحيح";
                isValid = false;
            }
            else
            {
                EmailError.Visibility = Visibility.Collapsed;
            }

            // Validate Password
            if (string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                PasswordError.Visibility = Visibility.Visible;
                PasswordError.Text = "يرجى إدخال كلمة المرور";
                PasswordStrengthPanel.Visibility = Visibility.Collapsed;
                isValid = false;
                errors.Add("كلمة المرور");
            }
            else if (PasswordBox.Password.Length < 6)
            {
                PasswordError.Visibility = Visibility.Visible;
                PasswordError.Text = "كلمة المرور يجب أن تكون 6 أحرف على الأقل";
                PasswordStrengthPanel.Visibility = Visibility.Visible;
                UpdatePasswordStrength(PasswordBox.Password);
                isValid = false;
            }
            else
            {
                PasswordError.Visibility = Visibility.Collapsed;
                PasswordStrengthPanel.Visibility = Visibility.Visible;
                UpdatePasswordStrength(PasswordBox.Password);
            }

            // Validate Confirm Password
            if (string.IsNullOrWhiteSpace(ConfirmPasswordBox.Password))
            {
                ConfirmPasswordError.Visibility = Visibility.Visible;
                ConfirmPasswordError.Text = "يرجى تأكيد كلمة المرور";
                isValid = false;
                errors.Add("تأكيد كلمة المرور");
            }
            else if (PasswordBox.Password != ConfirmPasswordBox.Password)
            {
                ConfirmPasswordError.Visibility = Visibility.Visible;
                ConfirmPasswordError.Text = "كلمة المرور غير متطابقة";
                isValid = false;
            }
            else
            {
                ConfirmPasswordError.Visibility = Visibility.Collapsed;
            }

            // Update Save button and validation summary
            SaveButton.IsEnabled = isValid;
            
            if (isValid)
            {
                ValidationSummary.Text = "✅ جميع البيانات صحيحة";
                ValidationSummary.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
            }
            else if (errors.Count > 0)
            {
                ValidationSummary.Text = $"يرجى إصلاح: {string.Join("، ", errors)}";
                ValidationSummary.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
            }
        }

        private void UpdatePasswordStrength(string password)
        {
            var strength = CalculatePasswordStrength(password);
            var strengthPercentage = strength / 4.0; // Max strength is 4

            PasswordStrengthFill.Width = strengthPercentage * 100;
            
            switch (strength)
            {
                case 0:
                case 1:
                    PasswordStrengthFill.Background = new SolidColorBrush(Color.FromRgb(244, 67, 54)); // Red
                    PasswordStrengthText.Text = "ضعيف";
                    PasswordStrengthText.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                    break;
                case 2:
                    PasswordStrengthFill.Background = new SolidColorBrush(Color.FromRgb(255, 152, 0)); // Orange
                    PasswordStrengthText.Text = "متوسط";
                    PasswordStrengthText.Foreground = new SolidColorBrush(Color.FromRgb(255, 152, 0));
                    break;
                case 3:
                    PasswordStrengthFill.Background = new SolidColorBrush(Color.FromRgb(255, 193, 7)); // Yellow
                    PasswordStrengthText.Text = "جيد";
                    PasswordStrengthText.Foreground = new SolidColorBrush(Color.FromRgb(255, 193, 7));
                    break;
                case 4:
                    PasswordStrengthFill.Background = new SolidColorBrush(Color.FromRgb(76, 175, 80)); // Green
                    PasswordStrengthText.Text = "قوي";
                    PasswordStrengthText.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
                    break;
            }
        }

        private int CalculatePasswordStrength(string password)
        {
            int strength = 0;
            
            if (password.Length >= 8) strength++;
            if (Regex.IsMatch(password, @"[a-z]")) strength++;
            if (Regex.IsMatch(password, @"[A-Z]")) strength++;
            if (Regex.IsMatch(password, @"[0-9]")) strength++;
            if (Regex.IsMatch(password, @"[!@#$%^&*(),.?""':{}|<>]")) strength++;
            
            return Math.Min(strength, 4);
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private bool IsValidUsername(string username)
        {
            return Regex.IsMatch(username, @"^[a-zA-Z0-9_]+$");
        }

        private void RoleComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateRoleDescription();
        }

        private void UpdateRoleDescription()
        {
            if (RoleComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var role = selectedItem.Tag?.ToString();
                RoleDescription.Text = role switch
                {
                    "Admin" => "مسئول النظام - صلاحيات كاملة لإدارة النظام والمستخدمين",
                    "Manager" => "مشرف - صلاحيات إدارية محدودة ومراقبة العمليات",
                    "User" => "مستخدم عادي - صلاحيات أساسية لاستخدام النظام",
                    "Viewer" => "مشاهد - صلاحيات قراءة فقط بدون تعديل",
                    _ => "مستخدم عادي مع صلاحيات محدودة"
                };
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show loading state
                SaveButton.IsEnabled = false;
                SaveButton.Content = "⏳ جاري إنشاء المستخدم...";
                CancelButton.IsEnabled = false;

                var selectedRole = ((ComboBoxItem)RoleComboBox.SelectedItem).Tag.ToString();
                
                var newUser = new User
                {
                    FullName = FullNameTextBox.Text.Trim(),
                    Username = UsernameTextBox.Text.Trim(),
                    Email = EmailTextBox.Text.Trim(),
                    Role = selectedRole ?? "User",
                    IsActive = IsActiveCheckBox.IsChecked ?? true,
                    Notes = NotesTextBox.Text.Trim(),
                    CreatedBy = "admin" // Should be current user
                };

                var success = await _userService.CreateUserAsync(newUser, PasswordBox.Password);
                
                if (success)
                {
                    MessageBox.Show(
                        $"تم إنشاء المستخدم '{newUser.FullName}' بنجاح!\n\n" +
                        $"اسم المستخدم: {newUser.Username}\n" +
                        $"الدور: {GetRoleDisplayName(newUser.Role)}\n" +
                        $"الحالة: {(newUser.IsActive ? "نشط" : "غير نشط")}\n\n" +
                        "يمكن للمستخدم الآن تسجيل الدخول للنظام.",
                        "تم إنشاء المستخدم بنجاح", 
                        MessageBoxButton.OK, 
                        MessageBoxImage.Information);
                    
                    UserAdded?.Invoke(this, EventArgs.Empty);
                    this.Close();
                }
                else
                {
                    MessageBox.Show(
                        "فشل في إنشاء المستخدم!\n\n" +
                        "الأسباب المحتملة:\n" +
                        "• اسم المستخدم موجود مسبقاً\n" +
                        "• البريد الإلكتروني مستخدم من قبل\n" +
                        "• خطأ في قاعدة البيانات\n\n" +
                        "يرجى المحاولة مرة أخرى بمعلومات مختلفة.",
                        "فشل في إنشاء المستخدم", 
                        MessageBoxButton.OK, 
                        MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ غير متوقع أثناء إنشاء المستخدم:\n\n{ex.Message}\n\n" +
                    "يرجى المحاولة مرة أخرى أو الاتصال بمسئول النظام.",
                    "خطأ في النظام", 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Error);
            }
            finally
            {
                // Restore button state
                SaveButton.IsEnabled = true;
                SaveButton.Content = "💾 إنشاء المستخدم";
                CancelButton.IsEnabled = true;
            }
        }

        private string GetRoleDisplayName(string role)
        {
            return role?.ToLower() switch
            {
                "admin" => "مسئول النظام",
                "manager" => "مشرف",
                "user" => "مستخدم",
                "viewer" => "مشاهد",
                _ => "مستخدم"
            };
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إلغاء إنشاء المستخدم؟\n\nسيتم فقدان جميع البيانات المدخلة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                this.Close();
            }
        }
    }
}
