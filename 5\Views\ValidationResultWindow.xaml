<Window x:Class="DriverManagementSystem.Views.ValidationResultWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="🔍 نتائج التحقق من صحة البيانات" Height="500" Width="700"
        WindowStartupLocation="CenterScreen"
        FlowDirection="RightToLeft"
        ResizeMode="CanResize">

    <Window.Resources>
        <!-- Modern Button Style like Excel Import with Icons -->
        <Style x:Key="FormalButtonStyle" TargetType="Button">
            <Setter Property="Height" Value="45"/>
            <Setter Property="FontWeight" Value="Bold"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="BorderThickness" Value="0"/>
            <Setter Property="Cursor" Value="Hand"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Border Background="{TemplateBinding Background}"
                               CornerRadius="6"
                               Padding="{TemplateBinding Padding}"
                               x:Name="border">
                            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" VerticalAlignment="Center">
                                <TextBlock x:Name="IconText" Text="{TemplateBinding Tag}"
                                          FontSize="16" FontWeight="Bold"
                                          Foreground="{TemplateBinding Foreground}"
                                          Margin="0,0,8,0" VerticalAlignment="Center"/>
                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center"/>
                            </StackPanel>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.9"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter TargetName="border" Property="Opacity" Value="0.8"/>
                            </Trigger>
                            <Trigger Property="IsEnabled" Value="False">
                                <Setter TargetName="border" Property="Opacity" Value="0.5"/>
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>

        <Style TargetType="Border" x:Key="SectionStyle">
            <Setter Property="Background" Value="White"/>
            <Setter Property="CornerRadius" Value="8"/>
            <Setter Property="Padding" Value="15"/>
            <Setter Property="Margin" Value="0,0,0,10"/>
            <Setter Property="Effect">
                <Setter.Value>
                    <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="2" Opacity="0.2"/>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>

    <Grid Background="#F8F9FA">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- Header -->
        <Border Grid.Row="0" Padding="20" Margin="0,0,0,15">
            <Border.Background>
                <LinearGradientBrush StartPoint="0,0" EndPoint="1,1">
                    <GradientStop Color="#667eea" Offset="0"/>
                    <GradientStop Color="#764ba2" Offset="1"/>
                </LinearGradientBrush>
            </Border.Background>
            <StackPanel>
                <TextBlock Text="🔍 نتائج التحقق من صحة البيانات" 
                          FontSize="20" FontWeight="Bold" Foreground="White" HorizontalAlignment="Center"/>
                <StackPanel Orientation="Horizontal" HorizontalAlignment="Center" Margin="0,10,0,0">
                    <TextBlock Text="درجة الجودة:" FontSize="14" Foreground="#E8E8E8" Margin="0,0,10,0"/>
                    <TextBlock Text="{Binding QualityScore}" FontSize="16" FontWeight="Bold" Foreground="White"/>
                    <TextBlock Text="%" FontSize="14" Foreground="#E8E8E8" Margin="2,0,10,0"/>
                    <TextBlock Text="{Binding QualityLevel}" FontSize="14" FontWeight="Bold" Foreground="#FFD700"/>
                </StackPanel>
            </StackPanel>
        </Border>

        <!-- Content -->
        <ScrollViewer Grid.Row="1" Margin="20,0" VerticalScrollBarVisibility="Auto">
            <StackPanel>

                <!-- Summary -->
                <Border Style="{StaticResource SectionStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="*"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" HorizontalAlignment="Center">
                            <TextBlock Text="✅" FontSize="30" HorizontalAlignment="Center"/>
                            <TextBlock Text="صحيح" FontSize="14" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#4CAF50"/>
                            <TextBlock Text="{Binding IsValidText}" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" HorizontalAlignment="Center">
                            <TextBlock Text="❌" FontSize="30" HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding ErrorsCount}" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#F44336"/>
                            <TextBlock Text="أخطاء" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" HorizontalAlignment="Center">
                            <TextBlock Text="⚠️" FontSize="30" HorizontalAlignment="Center"/>
                            <TextBlock Text="{Binding WarningsCount}" FontSize="18" FontWeight="Bold" HorizontalAlignment="Center" Foreground="#FF9800"/>
                            <TextBlock Text="تحذيرات" FontSize="12" HorizontalAlignment="Center" Foreground="#666"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- Errors Section -->
                <Border Style="{StaticResource SectionStyle}" Visibility="{Binding ErrorsVisibility}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBlock Text="❌" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="الأخطاء المكتشفة" FontSize="16" FontWeight="Bold" Foreground="#F44336"/>
                        </StackPanel>
                        
                        <ItemsControl ItemsSource="{Binding Errors}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#FFEBEE" CornerRadius="5" Padding="10" Margin="0,0,0,5">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="•" FontWeight="Bold" Foreground="#F44336" Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding}" TextWrapping="Wrap" Foreground="#C62828"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Warnings Section -->
                <Border Style="{StaticResource SectionStyle}" Visibility="{Binding WarningsVisibility}">
                    <StackPanel>
                        <StackPanel Orientation="Horizontal" Margin="0,0,0,10">
                            <TextBlock Text="⚠️" FontSize="16" Margin="0,0,8,0"/>
                            <TextBlock Text="التحذيرات" FontSize="16" FontWeight="Bold" Foreground="#FF9800"/>
                        </StackPanel>
                        
                        <ItemsControl ItemsSource="{Binding Warnings}">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate>
                                    <Border Background="#FFF8E1" CornerRadius="5" Padding="10" Margin="0,0,0,5">
                                        <StackPanel Orientation="Horizontal">
                                            <TextBlock Text="•" FontWeight="Bold" Foreground="#FF9800" Margin="0,0,8,0"/>
                                            <TextBlock Text="{Binding}" TextWrapping="Wrap" Foreground="#F57C00"/>
                                        </StackPanel>
                                    </Border>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>
                    </StackPanel>
                </Border>

                <!-- Success Message -->
                <Border Style="{StaticResource SectionStyle}" Visibility="{Binding SuccessVisibility}">
                    <StackPanel HorizontalAlignment="Center">
                        <TextBlock Text="🎉" FontSize="40" HorizontalAlignment="Center"/>
                        <TextBlock Text="تم التحقق بنجاح!" FontSize="18" FontWeight="Bold" 
                                  HorizontalAlignment="Center" Foreground="#4CAF50" Margin="0,10,0,0"/>
                        <TextBlock Text="جميع البيانات صحيحة ومتسقة" FontSize="14" 
                                  HorizontalAlignment="Center" Foreground="#666" Margin="0,5,0,0"/>
                    </StackPanel>
                </Border>

            </StackPanel>
        </ScrollViewer>

        <!-- Buttons -->
        <Border Grid.Row="2" Background="#F5F5F5" Padding="25">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="Auto"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <StackPanel Grid.Column="1" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Name="ContinueButton" Content="متابعة الاستيراد" Tag="▶"
                            Background="#4CAF50" Foreground="White"
                            Padding="25,12" Margin="0,0,15,0"
                            Click="ContinueButton_Click"
                            Visibility="{Binding ContinueButtonVisibility}"
                            Style="{StaticResource FormalButtonStyle}"/>

                    <Button Name="FixDataButton" Content="إصلاح البيانات" Tag="🔧"
                            Background="#FF9800" Foreground="White"
                            Padding="25,12" Margin="0,0,15,0"
                            Click="FixDataButton_Click"
                            Visibility="{Binding FixButtonVisibility}"
                            Style="{StaticResource FormalButtonStyle}"/>

                    <Button Name="CancelButton" Content="إلغاء" Tag="✕"
                            Background="#F44336" Foreground="White"
                            Padding="25,12"
                            Click="CancelButton_Click"
                            Style="{StaticResource FormalButtonStyle}"/>
                </StackPanel>
            </Grid>
        </Border>

    </Grid>
</Window>
