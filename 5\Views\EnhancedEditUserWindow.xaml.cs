using System;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class EnhancedEditUserWindow : Window
    {
        private readonly IUserService _userService;
        private readonly User _user;
        private readonly string _originalRole;
        public event EventHandler? UserUpdated;

        public EnhancedEditUserWindow(IUserService userService, User user)
        {
            InitializeComponent();
            _userService = userService;
            _user = user;
            _originalRole = user.Role;
            LoadUserData();
        }

        private void LoadUserData()
        {
            try
            {
                // Update header
                HeaderTitle.Text = $"تعديل المستخدم: {_user.FullName}";
                HeaderSubtitle.Text = $"تعديل بيانات المستخدم @{_user.Username}";
                
                // Update header user info
                UserNameHeader.Text = _user.FullName;
                UserDetailsHeader.Text = $"@{_user.Username} • {GetRoleDisplayName(_user.Role)}";
                UserIconHeader.Text = GetUserIcon(_user.Role);
                UserAvatarHeader.Background = GetRoleColor(_user.Role);

                // Load form data
                FullNameTextBox.Text = _user.FullName;
                UsernameTextBox.Text = _user.Username;
                EmailTextBox.Text = _user.Email;
                IsActiveCheckBox.IsChecked = _user.IsActive;
                NotesTextBox.Text = _user.Notes ?? "";

                // Set role
                foreach (ComboBoxItem item in RoleComboBox.Items)
                {
                    if (item.Tag.ToString() == _user.Role)
                    {
                        RoleComboBox.SelectedItem = item;
                        break;
                    }
                }

                // Update statistics
                CreatedDateText.Text = _user.CreatedDate.ToString("yyyy/MM/dd HH:mm");
                LastLoginText.Text = _user.LastLoginDate.HasValue 
                    ? _user.LastLoginDate.Value.ToString("yyyy/MM/dd HH:mm")
                    : "لم يسجل دخول من قبل";

                // Update active status description
                UpdateActiveStatusDescription();

                // Disable certain fields for admin user
                if (_user.Username == "admin")
                {
                    UsernameTextBox.IsEnabled = false;
                    RoleComboBox.IsEnabled = false;
                    IsActiveCheckBox.IsEnabled = false;
                    UsernameNote.Text = "لا يمكن تعديل اسم المستخدم للمدير الرئيسي";
                    UsernameNote.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
                }

                ValidateForm();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في تحميل بيانات المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private void ValidateForm(object? sender = null, EventArgs? e = null)
        {
            bool isValid = true;

            // Validate Full Name
            if (string.IsNullOrWhiteSpace(FullNameTextBox.Text))
            {
                isValid = false;
            }

            // Validate Email
            if (string.IsNullOrWhiteSpace(EmailTextBox.Text) || !IsValidEmail(EmailTextBox.Text.Trim()))
            {
                isValid = false;
            }

            // Validate password if provided
            if (!string.IsNullOrWhiteSpace(PasswordBox.Password))
            {
                if (PasswordBox.Password.Length < 6)
                {
                    isValid = false;
                }

                if (PasswordBox.Password != ConfirmPasswordBox.Password)
                {
                    PasswordError.Visibility = Visibility.Visible;
                    isValid = false;
                }
                else
                {
                    PasswordError.Visibility = Visibility.Collapsed;
                }
            }
            else
            {
                PasswordError.Visibility = Visibility.Collapsed;
            }

            SaveButton.IsEnabled = isValid;
        }

        private bool IsValidEmail(string email)
        {
            try
            {
                var addr = new System.Net.Mail.MailAddress(email);
                return addr.Address == email;
            }
            catch
            {
                return false;
            }
        }

        private void RoleComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (RoleComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var newRole = selectedItem.Tag?.ToString();
                if (newRole != _originalRole)
                {
                    RoleWarning.Visibility = Visibility.Visible;
                }
                else
                {
                    RoleWarning.Visibility = Visibility.Collapsed;
                }
            }
        }

        private void ActiveStatus_Changed(object sender, RoutedEventArgs e)
        {
            UpdateActiveStatusDescription();
        }

        private void UpdateActiveStatusDescription()
        {
            if (IsActiveCheckBox.IsChecked == true)
            {
                ActiveStatusDescription.Text = "المستخدم نشط ويمكنه تسجيل الدخول";
                ActiveStatusDescription.Foreground = new SolidColorBrush(Color.FromRgb(76, 175, 80));
            }
            else
            {
                ActiveStatusDescription.Text = "المستخدم غير نشط ولا يمكنه تسجيل الدخول";
                ActiveStatusDescription.Foreground = new SolidColorBrush(Color.FromRgb(244, 67, 54));
            }
        }

        private async void SaveButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // Show loading state
                SaveButton.IsEnabled = false;
                SaveButton.Content = "⏳ جاري الحفظ...";
                CancelButton.IsEnabled = false;
                ResetPasswordButton.IsEnabled = false;

                var selectedRole = ((ComboBoxItem)RoleComboBox.SelectedItem).Tag.ToString();
                
                // Update user data
                _user.FullName = FullNameTextBox.Text.Trim();
                _user.Email = EmailTextBox.Text.Trim();
                _user.Role = selectedRole ?? _user.Role;
                _user.IsActive = IsActiveCheckBox.IsChecked ?? true;
                _user.Notes = NotesTextBox.Text.Trim();

                var success = await _userService.UpdateUserAsync(_user);
                
                if (success)
                {
                    // Update password if provided
                    if (!string.IsNullOrWhiteSpace(PasswordBox.Password))
                    {
                        await _userService.ChangePasswordAsync(_user.UserId, PasswordBox.Password);
                    }

                    MessageBox.Show(
                        $"تم تحديث بيانات المستخدم '{_user.FullName}' بنجاح!\n\n" +
                        "التغييرات المطبقة:\n" +
                        $"• الاسم: {_user.FullName}\n" +
                        $"• البريد الإلكتروني: {_user.Email}\n" +
                        $"• الدور: {GetRoleDisplayName(_user.Role)}\n" +
                        $"• الحالة: {(_user.IsActive ? "نشط" : "غير نشط")}" +
                        (!string.IsNullOrWhiteSpace(PasswordBox.Password) ? "\n• تم تحديث كلمة المرور" : ""),
                        "تم التحديث بنجاح", 
                        MessageBoxButton.OK, 
                        MessageBoxImage.Information);
                    
                    UserUpdated?.Invoke(this, EventArgs.Empty);
                    this.Close();
                }
                else
                {
                    MessageBox.Show(
                        "فشل في تحديث بيانات المستخدم!\n\n" +
                        "الأسباب المحتملة:\n" +
                        "• البريد الإلكتروني مستخدم من قبل مستخدم آخر\n" +
                        "• خطأ في قاعدة البيانات\n" +
                        "• مشكلة في الاتصال\n\n" +
                        "يرجى المحاولة مرة أخرى.",
                        "فشل في التحديث", 
                        MessageBoxButton.OK, 
                        MessageBoxImage.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(
                    $"حدث خطأ غير متوقع أثناء تحديث المستخدم:\n\n{ex.Message}\n\n" +
                    "يرجى المحاولة مرة أخرى أو الاتصال بمسئول النظام.",
                    "خطأ في النظام", 
                    MessageBoxButton.OK, 
                    MessageBoxImage.Error);
            }
            finally
            {
                // Restore button state
                SaveButton.IsEnabled = true;
                SaveButton.Content = "💾 حفظ التغييرات";
                CancelButton.IsEnabled = true;
                ResetPasswordButton.IsEnabled = true;
            }
        }

        private async void ResetPasswordButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var result = MessageBox.Show(
                    $"هل أنت متأكد من إعادة تعيين كلمة مرور المستخدم '{_user.FullName}'؟\n\n" +
                    "سيتم تعيين كلمة مرور مؤقتة جديدة وسيحتاج المستخدم لتغييرها عند تسجيل الدخول التالي.",
                    "تأكيد إعادة تعيين كلمة المرور",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    // Generate temporary password
                    var tempPassword = GenerateTemporaryPassword();
                    
                    var success = await _userService.ChangePasswordAsync(_user.UserId, tempPassword);
                    
                    if (success)
                    {
                        MessageBox.Show(
                            $"تم إعادة تعيين كلمة المرور بنجاح!\n\n" +
                            $"كلمة المرور المؤقتة الجديدة: {tempPassword}\n\n" +
                            "يرجى إبلاغ المستخدم بكلمة المرور الجديدة وطلب تغييرها عند تسجيل الدخول التالي.",
                            "تم إعادة تعيين كلمة المرور", 
                            MessageBoxButton.OK, 
                            MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show(
                            "فشل في إعادة تعيين كلمة المرور!\n\nيرجى المحاولة مرة أخرى.",
                            "فشل في العملية", 
                            MessageBoxButton.OK, 
                            MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في إعادة تعيين كلمة المرور: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        private string GenerateTemporaryPassword()
        {
            var random = new Random();
            var chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            var password = "";
            for (int i = 0; i < 8; i++)
            {
                password += chars[random.Next(chars.Length)];
            }
            return password;
        }

        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            var result = MessageBox.Show(
                "هل أنت متأكد من إلغاء التعديل؟\n\nسيتم فقدان جميع التغييرات غير المحفوظة.",
                "تأكيد الإلغاء",
                MessageBoxButton.YesNo,
                MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                this.Close();
            }
        }

        private Brush GetRoleColor(string role)
        {
            return role?.ToLower() switch
            {
                "admin" => new SolidColorBrush(Color.FromRgb(244, 67, 54)),
                "manager" => new SolidColorBrush(Color.FromRgb(33, 150, 243)),
                "user" => new SolidColorBrush(Color.FromRgb(76, 175, 80)),
                "viewer" => new SolidColorBrush(Color.FromRgb(156, 39, 176)),
                _ => new SolidColorBrush(Color.FromRgb(158, 158, 158))
            };
        }

        private string GetUserIcon(string role)
        {
            return role?.ToLower() switch
            {
                "admin" => "👨‍💼",
                "manager" => "👨‍💻",
                "user" => "👤",
                "viewer" => "👁️",
                _ => "👤"
            };
        }

        private string GetRoleDisplayName(string role)
        {
            return role?.ToLower() switch
            {
                "admin" => "مسئول النظام",
                "manager" => "مشرف",
                "user" => "مستخدم",
                "viewer" => "مشاهد",
                _ => "مستخدم"
            };
        }
    }
}
