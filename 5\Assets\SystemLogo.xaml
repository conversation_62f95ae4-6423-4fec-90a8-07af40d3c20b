<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!-- Main System Logo -->
    <Canvas x:Key="SystemLogo" Width="120" Height="120">
        <!-- Background Circle with Gradient -->
        <Ellipse Width="120" Height="120">
            <Ellipse.Fill>
                <RadialGradientBrush>
                    <GradientStop Color="#2196F3" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="1"/>
                </RadialGradientBrush>
            </Ellipse.Fill>
            <Ellipse.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="3" Opacity="0.3" BlurRadius="8"/>
            </Ellipse.Effect>
        </Ellipse>
        
        <!-- Truck Body -->
        <Rectangle Width="50" Height="20" 
                   Canvas.Left="25" Canvas.Top="45"
                   Fill="White" 
                   RadiusX="3" RadiusY="3"/>
        
        <!-- Truck Cabin -->
        <Rectangle Width="15" Height="25" 
                   Canvas.Left="25" Canvas.Top="35"
                   Fill="White" 
                   RadiusX="2" RadiusY="2"/>
        
        <!-- Truck Front -->
        <Rectangle Width="8" Height="20" 
                   Canvas.Left="17" Canvas.Top="40"
                   Fill="White" 
                   RadiusX="2" RadiusY="2"/>
        
        <!-- Wheels -->
        <Ellipse Width="12" Height="12" 
                 Canvas.Left="30" Canvas.Top="62"
                 Fill="#1976D2" 
                 Stroke="White" 
                 StrokeThickness="2"/>
        <Ellipse Width="12" Height="12" 
                 Canvas.Left="58" Canvas.Top="62"
                 Fill="#1976D2" 
                 Stroke="White" 
                 StrokeThickness="2"/>
        
        <!-- System Text -->
        <TextBlock Text="DMS" 
                   Canvas.Left="35" Canvas.Top="80"
                   FontFamily="Arial Black"
                   FontSize="16" 
                   FontWeight="Bold"
                   Foreground="White"
                   HorizontalAlignment="Center"/>
        
        <!-- Decorative Elements -->
        <Ellipse Width="4" Height="4" 
                 Canvas.Left="90" Canvas.Top="25"
                 Fill="White" 
                 Opacity="0.7"/>
        <Ellipse Width="3" Height="3" 
                 Canvas.Left="95" Canvas.Top="35"
                 Fill="White" 
                 Opacity="0.5"/>
        <Ellipse Width="2" Height="2" 
                 Canvas.Left="85" Canvas.Top="30"
                 Fill="White" 
                 Opacity="0.6"/>
    </Canvas>
    
    <!-- Small Logo for Menu -->
    <Canvas x:Key="SmallSystemLogo" Width="50" Height="50">
        <!-- Background Circle -->
        <Ellipse Width="50" Height="50">
            <Ellipse.Fill>
                <RadialGradientBrush>
                    <GradientStop Color="#2196F3" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="1"/>
                </RadialGradientBrush>
            </Ellipse.Fill>
        </Ellipse>
        
        <!-- Mini Truck -->
        <Rectangle Width="20" Height="8" 
                   Canvas.Left="10" Canvas.Top="18"
                   Fill="White" 
                   RadiusX="1" RadiusY="1"/>
        <Rectangle Width="6" Height="10" 
                   Canvas.Left="10" Canvas.Top="14"
                   Fill="White" 
                   RadiusX="1" RadiusY="1"/>
        <Rectangle Width="4" Height="8" 
                   Canvas.Left="6" Canvas.Top="16"
                   Fill="White" 
                   RadiusX="1" RadiusY="1"/>
        
        <!-- Mini Wheels -->
        <Ellipse Width="5" Height="5" 
                 Canvas.Left="12" Canvas.Top="25"
                 Fill="#1976D2"/>
        <Ellipse Width="5" Height="5" 
                 Canvas.Left="23" Canvas.Top="25"
                 Fill="#1976D2"/>
        
        <!-- Mini Text -->
        <TextBlock Text="DMS" 
                   Canvas.Left="15" Canvas.Top="32"
                   FontFamily="Arial"
                   FontSize="8" 
                   FontWeight="Bold"
                   Foreground="White"/>
    </Canvas>
    
    <!-- Login Logo -->
    <Canvas x:Key="LoginLogo" Width="100" Height="100">
        <!-- Background with Animation Effect -->
        <Ellipse Width="100" Height="100">
            <Ellipse.Fill>
                <RadialGradientBrush>
                    <GradientStop Color="#2196F3" Offset="0"/>
                    <GradientStop Color="#1976D2" Offset="0.7"/>
                    <GradientStop Color="#0D47A1" Offset="1"/>
                </RadialGradientBrush>
            </Ellipse.Fill>
            <Ellipse.Effect>
                <DropShadowEffect Color="Gray" Direction="270" ShadowDepth="4" Opacity="0.4" BlurRadius="12"/>
            </Ellipse.Effect>
        </Ellipse>
        
        <!-- Inner Circle -->
        <Ellipse Width="80" Height="80" 
                 Canvas.Left="10" Canvas.Top="10"
                 Stroke="White" 
                 StrokeThickness="2" 
                 Fill="Transparent"
                 Opacity="0.3"/>
        
        <!-- Truck Design -->
        <Rectangle Width="40" Height="16" 
                   Canvas.Left="20" Canvas.Top="35"
                   Fill="White" 
                   RadiusX="2" RadiusY="2"/>
        <Rectangle Width="12" Height="20" 
                   Canvas.Left="20" Canvas.Top="27"
                   Fill="White" 
                   RadiusX="2" RadiusY="2"/>
        <Rectangle Width="6" Height="16" 
                   Canvas.Left="14" Canvas.Top="31"
                   Fill="White" 
                   RadiusX="1" RadiusY="1"/>
        
        <!-- Wheels with Details -->
        <Ellipse Width="10" Height="10" 
                 Canvas.Left="25" Canvas.Top="48"
                 Fill="#0D47A1" 
                 Stroke="White" 
                 StrokeThickness="2"/>
        <Ellipse Width="10" Height="10" 
                 Canvas.Left="45" Canvas.Top="48"
                 Fill="#0D47A1" 
                 Stroke="White" 
                 StrokeThickness="2"/>
        
        <!-- System Title -->
        <TextBlock Text="DMS" 
                   Canvas.Left="35" Canvas.Top="65"
                   FontFamily="Arial Black"
                   FontSize="14" 
                   FontWeight="Bold"
                   Foreground="White"/>
    </Canvas>
</ResourceDictionary>
