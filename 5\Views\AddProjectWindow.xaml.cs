using System.Windows;
using DriverManagementSystem.ViewModels;

namespace DriverManagementSystem.Views
{
    public partial class AddProjectWindow : Window
    {
        public AddProjectWindow()
        {
            InitializeComponent();

            try
            {
                DataContext = new AddProjectViewModel();
            }
            catch
            {
                // If ViewModel fails, show simple message
                MessageBox.Show("تم فتح نموذج إدارة المشاريع بنجاح!", "إدارة المشاريع",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }

            // Focus on project number textbox when window loads
            try
            {
                Loaded += (s, e) =>
                {
                    try
                    {
                        ProjectNumberTextBox?.Focus();
                    }
                    catch { }
                };
            }
            catch { }
        }
    }
}
