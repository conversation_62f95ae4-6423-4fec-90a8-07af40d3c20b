using System;
using System.Collections.Generic;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using DriverManagementSystem.Data;
using DriverManagementSystem.Models;
using DriverManagementSystem.Services;

namespace DriverManagementSystem.Views
{
    public partial class SimpleUserManagementView : UserControl
    {
        private IUserService? _userService;

        public SimpleUserManagementView()
        {
            InitializeComponent();
            InitializeAsync();
        }

        private async void InitializeAsync()
        {
            try
            {
                UsersPanel.Children.Clear();
                UsersPanel.Children.Add(new TextBlock 
                { 
                    Text = "جاري تهيئة قاعدة البيانات...", 
                    FontSize = 16, 
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(20)
                });

                // Initialize database and service
                var context = new ApplicationDbContext();
                await context.InitializeDatabaseAsync();
                _userService = new UserService(context);

                // Load users
                await LoadUsersAsync();
            }
            catch (Exception ex)
            {
                UsersPanel.Children.Clear();
                UsersPanel.Children.Add(new TextBlock 
                { 
                    Text = $"خطأ في التهيئة: {ex.Message}", 
                    FontSize = 14, 
                    Foreground = Brushes.Red,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(20),
                    TextWrapping = TextWrapping.Wrap
                });
            }
        }

        private async Task LoadUsersAsync()
        {
            try
            {
                if (_userService == null) return;

                UsersPanel.Children.Clear();
                UsersPanel.Children.Add(new TextBlock 
                { 
                    Text = "جاري تحميل المستخدمين...", 
                    FontSize = 16, 
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(20)
                });

                var users = await _userService.GetAllUsersAsync();
                
                UsersPanel.Children.Clear();

                if (users.Count == 0)
                {
                    UsersPanel.Children.Add(new TextBlock 
                    { 
                        Text = "لا توجد مستخدمين", 
                        FontSize = 16, 
                        HorizontalAlignment = HorizontalAlignment.Center,
                        Margin = new Thickness(20)
                    });
                    return;
                }

                foreach (var user in users)
                {
                    var userCard = CreateSimpleUserCard(user);
                    UsersPanel.Children.Add(userCard);
                }
            }
            catch (Exception ex)
            {
                UsersPanel.Children.Clear();
                UsersPanel.Children.Add(new TextBlock 
                { 
                    Text = $"خطأ في تحميل المستخدمين: {ex.Message}", 
                    FontSize = 14, 
                    Foreground = Brushes.Red,
                    HorizontalAlignment = HorizontalAlignment.Center,
                    Margin = new Thickness(20),
                    TextWrapping = TextWrapping.Wrap
                });
            }
        }

        private Border CreateSimpleUserCard(User user)
        {
            var card = new Border
            {
                Background = Brushes.White,
                BorderBrush = new SolidColorBrush(Color.FromRgb(224, 224, 224)),
                BorderThickness = new Thickness(1),
                CornerRadius = new CornerRadius(6),
                Padding = new Thickness(15),
                Margin = new Thickness(0, 0, 0, 10)
            };

            var grid = new Grid();
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = new GridLength(1, GridUnitType.Star) });
            grid.ColumnDefinitions.Add(new ColumnDefinition { Width = GridLength.Auto });

            // User Info
            var infoPanel = new StackPanel();
            Grid.SetColumn(infoPanel, 0);

            infoPanel.Children.Add(new TextBlock
            {
                Text = user.FullName,
                FontSize = 16,
                FontWeight = FontWeights.Bold,
                Foreground = new SolidColorBrush(Color.FromRgb(51, 51, 51))
            });

            infoPanel.Children.Add(new TextBlock
            {
                Text = $"@{user.Username} - {GetRoleDisplayName(user.Role)}",
                FontSize = 12,
                Foreground = new SolidColorBrush(Color.FromRgb(102, 102, 102)),
                Margin = new Thickness(0, 2, 0, 0)
            });

            infoPanel.Children.Add(new TextBlock
            {
                Text = user.Email,
                FontSize = 11,
                Foreground = new SolidColorBrush(Color.FromRgb(153, 153, 153)),
                Margin = new Thickness(0, 2, 0, 0)
            });

            // Status
            var statusText = new TextBlock
            {
                Text = user.IsActive ? "نشط ✅" : "غير نشط ❌",
                FontSize = 12,
                FontWeight = FontWeights.Bold,
                Foreground = user.IsActive ? Brushes.Green : Brushes.Red,
                VerticalAlignment = VerticalAlignment.Center
            };
            Grid.SetColumn(statusText, 1);

            grid.Children.Add(infoPanel);
            grid.Children.Add(statusText);
            card.Child = grid;

            return card;
        }

        private string GetRoleDisplayName(string role)
        {
            return role.ToLower() switch
            {
                "admin" => "مسئول النظام",
                "manager" => "مشرف",
                "user" => "مستخدم",
                "viewer" => "مشاهد",
                _ => "مستخدم"
            };
        }

        private void AddUserButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (_userService != null)
                {
                    var addUserWindow = new AddUserWindow(_userService);
                    addUserWindow.UserAdded += async (s, args) => await LoadUsersAsync();
                    addUserWindow.ShowDialog();
                }
                else
                {
                    MessageBox.Show("الخدمة غير متاحة حالياً", "خطأ", MessageBoxButton.OK, MessageBoxImage.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"خطأ في فتح نافذة إضافة المستخدم: {ex.Message}", "خطأ", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
